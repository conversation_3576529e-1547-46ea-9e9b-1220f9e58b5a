{"name": "@flowiq/public", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@flowiq/ui": "*", "@flowiq/types": "*", "@flowiq/utils": "*", "next": "14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.45.0", "@hookform/resolvers": "^3.1.0", "zod": "^3.21.0", "next-themes": "^0.2.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.0.0"}}