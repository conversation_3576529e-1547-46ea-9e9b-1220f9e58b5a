{"name": "@flowiq/auth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@flowiq/types": "*", "@flowiq/utils": "*", "next": "14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "zod": "^3.21.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.0.0"}}