#!/bin/bash

# FlowIQ Quick Start Script
# This script sets up the complete FlowIQ development environment

set -e  # Exit on any error

echo "🚀 FlowIQ Quick Start Setup"
echo "=========================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js found: $NODE_VERSION"
else
    print_error "Node.js not found. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "npm found: $NPM_VERSION"
else
    print_error "npm not found. Please install npm"
    exit 1
fi

# Check Python
if command_exists python3; then
    PYTHON_VERSION=$(python3 --version)
    print_success "Python found: $PYTHON_VERSION"
elif command_exists python; then
    PYTHON_VERSION=$(python --version)
    print_success "Python found: $PYTHON_VERSION"
else
    print_error "Python not found. Please install Python 3.11+"
    exit 1
fi

# Check PostgreSQL
if command_exists psql; then
    print_success "PostgreSQL found"
else
    print_warning "PostgreSQL not found. You'll need to install it manually."
    print_status "Install instructions:"
    print_status "  macOS: brew install postgresql"
    print_status "  Ubuntu: sudo apt install postgresql postgresql-contrib"
    print_status "  Windows: Download from https://www.postgresql.org/download/"
fi

# Check Redis
if command_exists redis-server; then
    print_success "Redis found"
else
    print_warning "Redis not found. You'll need to install it manually."
    print_status "Install instructions:"
    print_status "  macOS: brew install redis"
    print_status "  Ubuntu: sudo apt install redis-server"
    print_status "  Windows: Download from https://redis.io/download"
fi

echo ""
print_status "Setting up FlowIQ workspace..."

# Install frontend dependencies
print_status "Installing frontend dependencies..."
npm install

# Install workspace dependencies
print_status "Installing dashboard dependencies..."
npm install --workspace=applications/dashboard

print_status "Installing marketing dependencies..."
npm install --workspace=applications/marketing

print_status "Installing auth-service dependencies..."
npm install --workspace=applications/auth-service

print_success "Frontend dependencies installed"

# Setup environment files
print_status "Setting up environment files..."

if [ ! -f .env.local ]; then
    cp .env.example .env.local
    print_success "Created .env.local"
else
    print_warning ".env.local already exists"
fi

if [ ! -f applications/dashboard/.env.local ]; then
    cp .env.example applications/dashboard/.env.local
    print_success "Created dashboard .env.local"
fi

if [ ! -f applications/marketing/.env.local ]; then
    cp .env.example applications/marketing/.env.local
    print_success "Created marketing .env.local"
fi

if [ ! -f applications/auth-service/.env.local ]; then
    cp .env.example applications/auth-service/.env.local
    print_success "Created auth-service .env.local"
fi

# Setup backend
print_status "Setting up backend..."

cd backend

# Create virtual environment
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
    print_success "Virtual environment created"
else
    print_warning "Virtual environment already exists"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install Python dependencies
print_status "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements/development.txt
print_success "Python dependencies installed"

# Setup backend environment
if [ ! -f .env ]; then
    cp .env.example .env
    print_success "Created backend .env file"
    print_warning "Please update backend/.env with your database credentials"
else
    print_warning "Backend .env already exists"
fi

# Go back to root
cd ..

print_success "Setup complete!"
echo ""
echo "🎉 FlowIQ is ready for development!"
echo ""
echo "📋 Next Steps:"
echo "1. Update environment variables in .env.local files"
echo "2. Set up your PostgreSQL database:"
echo "   createdb flowiq_dev"
echo "   createuser flowiq_user"
echo ""
echo "3. Run database migrations:"
echo "   cd backend && source venv/bin/activate"
echo "   python manage.py migrate"
echo "   python manage.py createsuperuser"
echo ""
echo "4. Start development servers:"
echo "   npm run dev:dashboard    # Dashboard (port 3000)"
echo "   npm run dev:marketing    # Marketing (port 3001)"
echo "   npm run dev:auth-service # Auth Service (port 3002)"
echo ""
echo "   # In separate terminal for backend:"
echo "   cd backend && source venv/bin/activate"
echo "   python manage.py runserver"
echo ""
echo "5. Access your applications:"
echo "   Dashboard:  http://localhost:3000"
echo "   Marketing:  http://localhost:3001"
echo "   Auth:       http://localhost:3002"
echo "   Backend:    http://localhost:8000"
echo ""
echo "📚 For detailed setup instructions, see SETUP_GUIDE.md"
echo ""
print_success "Happy coding! 🚀"
