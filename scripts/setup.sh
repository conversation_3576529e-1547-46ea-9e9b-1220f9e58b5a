#!/bin/bash

echo "🚀 Setting up FlowIQ Workspace..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install root dependencies
echo "📦 Installing workspace dependencies..."
npm install

# Install dependencies for each application
echo "📦 Installing dashboard dependencies..."
npm install --workspace=applications/dashboard

echo "📦 Installing marketing dependencies..."
npm install --workspace=applications/marketing

echo "📦 Installing auth-service dependencies..."
npm install --workspace=applications/auth-service

# Create environment files
echo "🔧 Setting up environment files..."
if [ ! -f .env.local ]; then
    cp .env.example .env.local
    echo "✅ Created .env.local (please update with your values)"
fi

if [ ! -f applications/dashboard/.env.local ]; then
    cp .env.example applications/dashboard/.env.local
    echo "✅ Created dashboard .env.local"
fi

if [ ! -f applications/marketing/.env.local ]; then
    cp .env.example applications/marketing/.env.local
    echo "✅ Created marketing .env.local"
fi

if [ ! -f applications/auth-service/.env.local ]; then
    cp .env.example applications/auth-service/.env.local
    echo "✅ Created auth-service .env.local"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update .env.local files with your actual values"
echo "2. Start development servers:"
echo "   npm run dev:dashboard    # Dashboard (port 3000)"
echo "   npm run dev:marketing    # Marketing (port 3001)"
echo "   npm run dev:auth-service # Auth Service (port 3002)"
echo ""
echo "3. Visit http://localhost:3000 to see your dashboard"
echo "4. Visit http://localhost:3001 to see your marketing site"
echo ""
echo "Happy coding! 🚀"
