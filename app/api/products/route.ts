import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { productSchema } from "@/lib/validators/product-validators"
import { BarcodeService } from "@/lib/services/barcode-service"

// Mock database - replace with actual database calls
const mockProducts = [
  {
    id: "1",
    organizationId: "org-1",
    name: "Sample Product",
    sku: "PROD-000001",
    barcode: "2001234567890",
    barcodeType: "EAN13",
    costPrice: 10.0,
    sellingPrice: 15.0,
    vatRate: 15.0,
    trackInventory: true,
    lowStockThreshold: 10,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const search = searchParams.get("search") || ""
    const categoryId = searchParams.get("categoryId")
    const isActive = searchParams.get("isActive")

    // TODO: Replace with actual database query
    const filteredProducts = mockProducts.filter((product) => {
      if (search && !product.name.toLowerCase().includes(search.toLowerCase())) {
        return false
      }
      if (categoryId && product.categoryId !== categoryId) {
        return false
      }
      if (isActive !== null && product.isActive !== (isActive === "true")) {
        return false
      }
      return true
    })

    const total = filteredProducts.length
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const products = filteredProducts.slice(startIndex, endIndex)

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching products:", error)
    return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = productSchema.parse(body)

    // Validate barcode
    const barcodeValidation = BarcodeService.validateBarcode(validatedData.barcode, validatedData.barcodeType)

    if (!barcodeValidation.isValid) {
      return NextResponse.json({ error: "Invalid barcode", details: barcodeValidation.errors }, { status: 400 })
    }

    // TODO: Check for duplicate SKU/barcode in organization
    const existingProduct = mockProducts.find((p) => p.sku === validatedData.sku || p.barcode === validatedData.barcode)

    if (existingProduct) {
      return NextResponse.json({ error: "Product with this SKU or barcode already exists" }, { status: 409 })
    }

    // TODO: Replace with actual database insert
    const newProduct = {
      id: `prod-${Date.now()}`,
      organizationId: session.user.organizationId || "org-1",
      ...validatedData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    mockProducts.push(newProduct)

    return NextResponse.json(newProduct, { status: 201 })
  } catch (error) {
    console.error("Error creating product:", error)

    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json({ error: "Validation failed", details: error.message }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to create product" }, { status: 500 })
  }
}
