import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { productSchema } from "@/lib/validators/product-validators"
import { BarcodeService } from "@/lib/services/barcode-service"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // TODO: Replace with actual database query
    const product = {
      id,
      organizationId: "org-1",
      name: "Sample Product",
      description: "A sample product for testing",
      sku: "PROD-000001",
      barcode: "2001234567890",
      barcodeType: "EAN13",
      costPrice: 10.0,
      sellingPrice: 15.0,
      vatRate: 15.0,
      trackInventory: true,
      lowStockThreshold: 10,
      weight: 0.5,
      dimensions: {
        length: 10,
        width: 5,
        height: 2,
      },
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      inventory: [
        {
          id: "inv-1",
          productId: id,
          locationId: "loc-1",
          quantityOnHand: 100,
          quantityReserved: 5,
          quantityAvailable: 95,
          reorderPoint: 10,
          reorderQuantity: 50,
          location: {
            id: "loc-1",
            name: "Main Store",
            code: "MAIN",
          },
        },
      ],
    }

    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error("Error fetching product:", error)
    return NextResponse.json({ error: "Failed to fetch product" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const validatedData = productSchema.parse(body)

    // Validate barcode
    const barcodeValidation = BarcodeService.validateBarcode(validatedData.barcode, validatedData.barcodeType)

    if (!barcodeValidation.isValid) {
      return NextResponse.json({ error: "Invalid barcode", details: barcodeValidation.errors }, { status: 400 })
    }

    // TODO: Replace with actual database update
    const updatedProduct = {
      id,
      organizationId: session.user.organizationId || "org-1",
      ...validatedData,
      updatedAt: new Date().toISOString(),
    }

    return NextResponse.json(updatedProduct)
  } catch (error) {
    console.error("Error updating product:", error)

    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json({ error: "Validation failed", details: error.message }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to update product" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // TODO: Replace with actual database soft delete
    // Check if product has transactions before deleting
    const hasTransactions = false // TODO: Check actual transactions

    if (hasTransactions) {
      return NextResponse.json(
        { error: "Cannot delete product with existing transactions. Deactivate instead." },
        { status: 409 },
      )
    }

    return NextResponse.json({ message: "Product deleted successfully" })
  } catch (error) {
    console.error("Error deleting product:", error)
    return NextResponse.json({ error: "Failed to delete product" }, { status: 500 })
  }
}
