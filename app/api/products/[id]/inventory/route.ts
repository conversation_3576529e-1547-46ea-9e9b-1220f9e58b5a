import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { inventoryAdjustmentSchema } from "@/lib/validators/product-validators"
import { InventoryService } from "@/lib/services/inventory-service"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // TODO: Replace with actual database query
    const inventory = [
      {
        id: "inv-1",
        productId: id,
        locationId: "loc-1",
        quantityOnHand: 100,
        quantityReserved: 5,
        quantityAvailable: 95,
        reorderPoint: 10,
        reorderQuantity: 50,
        lastSyncAt: new Date().toISOString(),
        location: {
          id: "loc-1",
          name: "Main Store",
          code: "MAIN",
        },
      },
    ]

    // Add reorder suggestions
    const inventoryWithSuggestions = inventory.map((inv) => ({
      ...inv,
      reorderSuggestion: InventoryService.calculateReorderSuggestion(inv),
    }))

    return NextResponse.json(inventoryWithSuggestions)
  } catch (error) {
    console.error("Error fetching inventory:", error)
    return NextResponse.json({ error: "Failed to fetch inventory" }, { status: 500 })
  }
}

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const validatedData = inventoryAdjustmentSchema.parse(body)

    // TODO: Get current inventory level from database
    const currentInventory = {
      id: "inv-1",
      productId: id,
      locationId: validatedData.locationId,
      quantityOnHand: 100,
      quantityReserved: 5,
      quantityAvailable: 95,
      reorderPoint: 10,
      reorderQuantity: 50,
    }

    // Validate the movement
    const validation = InventoryService.validateMovement(
      currentInventory.quantityOnHand,
      "ADJUSTMENT",
      validatedData.quantity,
    )

    if (!validation.isValid) {
      return NextResponse.json({ error: "Invalid inventory adjustment", details: validation.errors }, { status: 400 })
    }

    // Calculate new levels
    const newLevels = InventoryService.calculateNewLevels(currentInventory, "ADJUSTMENT", validatedData.quantity)

    // Create movement record
    const movementRecord = InventoryService.createMovementRecord(
      validatedData.productId,
      validatedData.locationId,
      "ADJUSTMENT",
      validatedData.quantity,
      validatedData.reason,
      session.user.id!,
      validatedData.variantId,
      validatedData.reference,
    )

    // TODO: Save to database in transaction
    const updatedInventory = {
      ...currentInventory,
      ...newLevels,
      lastSyncAt: new Date().toISOString(),
    }

    return NextResponse.json({
      inventory: updatedInventory,
      movement: {
        id: `mov-${Date.now()}`,
        ...movementRecord,
        createdAt: new Date().toISOString(),
      },
    })
  } catch (error) {
    console.error("Error adjusting inventory:", error)

    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json({ error: "Validation failed", details: error.message }, { status: 400 })
    }

    return NextResponse.json({ error: "Failed to adjust inventory" }, { status: 500 })
  }
}
