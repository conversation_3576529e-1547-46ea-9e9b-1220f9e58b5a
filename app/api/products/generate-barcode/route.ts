import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { BarcodeService } from "@/lib/services/barcode-service"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { type = "EAN13", prefix = "200" } = body

    let barcode: string

    switch (type) {
      case "EAN13":
        barcode = BarcodeService.generateEAN13(prefix)
        break
      default:
        return NextResponse.json({ error: "Unsupported barcode type" }, { status: 400 })
    }

    // Validate the generated barcode
    const validation = BarcodeService.validateBarcode(barcode, type)

    if (!validation.isValid) {
      return NextResponse.json({ error: "Failed to generate valid barcode" }, { status: 500 })
    }

    return NextResponse.json({
      barcode,
      type,
      validation,
    })
  } catch (error) {
    console.error("Error generating barcode:", error)
    return NextResponse.json({ error: "Failed to generate barcode" }, { status: 500 })
  }
}
