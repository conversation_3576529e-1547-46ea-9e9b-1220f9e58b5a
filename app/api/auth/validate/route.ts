import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/services/auth-service"

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization")
    const cookieToken = request.cookies.get("auth-token")?.value

    const token = authHeader?.replace("Bearer ", "") || cookieToken

    if (!token) {
      return NextResponse.json({ valid: false, message: "No token provided" }, { status: 401 })
    }

    const result = await AuthService.validateSession(token)

    return NextResponse.json(result, {
      status: result.valid ? 200 : 401,
    })
  } catch (error) {
    console.error("Session validation API error:", error)
    return NextResponse.json({ valid: false, message: "Internal server error" }, { status: 500 })
  }
}
