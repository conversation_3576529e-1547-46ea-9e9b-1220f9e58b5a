import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/services/auth-service"
import { JWTUtils } from "@/lib/utils/jwt"

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get("authorization")
    const cookieToken = request.cookies.get("auth-token")?.value

    const token = authHeader?.replace("Bearer ", "") || cookieToken

    if (token) {
      const payload = JWTUtils.verifyAccessToken(token)
      if (payload) {
        await AuthService.logout(payload.sessionId)
      }
    }

    const response = NextResponse.json({
      success: true,
      message: "Logout successful",
    })

    // Clear cookies
    response.cookies.delete("auth-token")
    response.cookies.delete("refresh-token")

    return response
  } catch (error) {
    console.error("Logout API error:", error)
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 })
  }
}
