import { type NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { AuthService } from "@/lib/services/auth-service"
import { applyRateLimit } from "@/lib/middleware/rate-limiter"

const resetPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResponse = await applyRateLimit(request, "password-reset")
    if (rateLimitResponse) {
      return rateLimitResponse
    }

    const body = await request.json()

    // Validate input
    const validation = resetPasswordSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email address",
          errors: validation.error.errors,
        },
        { status: 400 },
      )
    }

    const result = await AuthService.requestPasswordReset(validation.data.email)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Password reset API error:", error)
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 })
  }
}
