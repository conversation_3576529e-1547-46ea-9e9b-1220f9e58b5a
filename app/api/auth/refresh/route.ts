import { type NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/services/auth-service"

export async function POST(request: NextRequest) {
  try {
    const refreshToken = request.cookies.get("refresh-token")?.value

    if (!refreshToken) {
      return NextResponse.json({ success: false, message: "No refresh token provided" }, { status: 401 })
    }

    const result = await AuthService.refreshToken(refreshToken)

    const response = NextResponse.json(result, {
      status: result.success ? 200 : 401,
    })

    // Update cookies if refresh successful
    if (result.success && result.token && result.refreshToken) {
      const isProduction = process.env.NODE_ENV === "production"

      response.cookies.set("auth-token", result.token, {
        httpOnly: true,
        secure: isProduction,
        sameSite: "strict",
        maxAge: 24 * 60 * 60, // 24 hours
        path: "/",
      })

      response.cookies.set("refresh-token", result.refreshToken, {
        httpOnly: true,
        secure: isProduction,
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: "/",
      })
    }

    return response
  } catch (error) {
    console.error("Token refresh API error:", error)
    return NextResponse.json({ success: false, message: "Internal server error" }, { status: 500 })
  }
}
