import { NextResponse } from "next/server"

// TODO: IMPLEMENT PRODUCTION API - Replace with actual transaction data
export async function GET() {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1500))

  return NextResponse.json({
    transactions: [
      { id: "TX-001", customer: "Acme Corp", amount: "R 1,200.00", status: "completed", date: "2023-05-01" },
      { id: "TX-002", customer: "Globex Inc", amount: "R 850.50", status: "pending", date: "2023-05-02" },
      { id: "TX-003", customer: "Stark Industries", amount: "R 3,200.75", status: "completed", date: "2023-05-03" },
      { id: "TX-004", customer: "Wayne Enterprises", amount: "R 1,750.25", status: "failed", date: "2023-05-04" },
      { id: "TX-005", customer: "Umbrella Corp", amount: "R 920.00", status: "completed", date: "2023-05-05" },
    ],
  })
}
