import { NextResponse } from "next/server";

// TODO: FLOWIQ PRODUCTION - Replace with actual FlowIQ business metrics data
export async function GET() {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return NextResponse.json({
    metrics: [
      {
        id: 1,
        label: "Total Revenue",
        value: "R 42,500",
        change: "+12.5%",
        trend: "up",
      },
      {
        id: 2,
        label: "Active Users",
        value: "1,234",
        change: "+5.3%",
        trend: "up",
      },
      {
        id: 3,
        label: "Conversion Rate",
        value: "3.2%",
        change: "-0.8%",
        trend: "down",
      },
      {
        id: 4,
        label: "Avg. Session",
        value: "2m 45s",
        change: "+10.1%",
        trend: "up",
      },
    ],
  });
}
