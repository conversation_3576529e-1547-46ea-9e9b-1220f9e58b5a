import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { TeamManagement } from "@/components/team/team-management"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UserPlus } from "lucide-react"

export default function TeamPage() {
  return (
    <div className="space-y-6">
      <DashboardHeader
        title="Team Management"
        description="Manage your team members and their permissions"
        action={
          <Button size="sm">
            <UserPlus className="mr-2 h-4 w-4" />
            Invite Member
          </Button>
        }
      />

      <TeamManagement />
    </div>
  )
}
