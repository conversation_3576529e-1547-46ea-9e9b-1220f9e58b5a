import { Suspense } from "react"

import { <PERSON><PERSON><PERSON>ead<PERSON> } from "@/components/header"
import { AnalyticsMetrics } from "@/components/metrics/analytics"
import { AnalyticsCharts } from "@/components/charts/analytics"
import { AnalyticsMetricsSkeleton } from "@/components/skeletons/analytics-metrics-skeleton"
import { AnalyticsChartsSkeleton } from "@/components/skeletons/analytics-charts-skeleton"

import { FeatureGate } from "@/components/access-control/feature-gate"
import { AccessBanner } from "@/components/access-control/access-banner"

export default async function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <AccessBanner />

      <FeatureGate feature="realTimeAnalytics">
        <DashboardHeader title="Analytics" description="Real-time insights and AI-powered business analytics" />

        <Suspense fallback={<AnalyticsMetricsSkeleton />}>
          <AnalyticsMetrics />
        </Suspense>

        <Suspense fallback={<AnalyticsChartsSkeleton />}>
          <AnalyticsCharts />
        </Suspense>
      </FeatureGate>
    </div>
  )
}
