import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DataManagement } from "@/components/data/data-management"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Upload, Download } from "lucide-react"

export default function DataPage() {
  return (
    <div className="space-y-6">
      <DashboardHeader
        title="Data Management"
        description="Import, export, and manage your business data"
        action={
          <div className="flex gap-2">
            <Button size="sm" variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import
            </Button>
            <Button size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        }
      />

      <DataManagement />
    </div>
  )
}
