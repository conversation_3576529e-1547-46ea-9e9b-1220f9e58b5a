import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardCards } from "@/components/dashboard/dashboard-cards"
import { DataTable } from "@/components/dashboard/data-table"
import { Suspense } from "react"
import { DashboardCardsSkeleton } from "@/components/dashboard/dashboard-cards-skeleton"
import { DataTableSkeleton } from "@/components/dashboard/data-table-skeleton"
import { AccessBanner } from "@/components/access-control/access-banner"
import { FeatureGate } from "@/components/access-control/feature-gate"
import { DemoModeBanner } from "@/components/access-control/demo-mode-banner"
import { UsageLimits } from "@/components/access-control/usage-limits"

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <AccessBanner />
      <DemoModeBanner demoType="dashboard" />

      <DashboardHeader title="Dashboard" description="Overview of your business metrics" />

      <FeatureGate feature="fullDashboard" showUpgradePrompt={false}>
        <Suspense fallback={<DashboardCardsSkeleton />}>
          <DashboardCards />
        </Suspense>
      </FeatureGate>

      <FeatureGate
        feature="fullDashboard"
        fallback={
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <DashboardCardsSkeleton />
          </div>
        }
      >
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Recent Transactions</h2>
          <FeatureGate feature="realTimeAnalytics">
            <Suspense fallback={<DataTableSkeleton />}>
              <DataTable />
            </Suspense>
          </FeatureGate>
        </div>
      </FeatureGate>

      <div className="grid gap-6 md:grid-cols-2">
        <FeatureGate feature="fullDashboard">
          <UsageLimits currentProducts={12} currentTransactions={45} currentTeamMembers={1} />
        </FeatureGate>
      </div>
    </div>
  )
}
