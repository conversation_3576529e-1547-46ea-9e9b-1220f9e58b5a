import type React from "react"
import { OnboardingProgress } from "@/components/onboarding/onboarding-progress"
import { Card, CardContent } from "@/components/ui/card"

export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="max-w-3xl mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Complete Your Setup</h1>
      <OnboardingProgress />
      <Card className="mt-6">
        <CardContent className="pt-6">{children}</CardContent>
      </Card>
    </div>
  )
}
