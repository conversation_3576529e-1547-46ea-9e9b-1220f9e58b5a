import type React from "react"
import Link from "next/link"
import { Logo } from "@/components/logo"

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12">
      <div className="mb-8">
        <Link href="/">
          <Logo className="h-10 w-10" />
        </Link>
      </div>
      <main className="w-full max-w-md">{children}</main>
    </div>
  )
}
