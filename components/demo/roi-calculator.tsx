"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Calculator, TrendingUp, Clock, DollarSign } from "lucide-react"
import Link from "next/link"

export function ROICalculator() {
  const [inputs, setInputs] = useState({
    monthlyRevenue: "",
    timeSpentOnBooks: "",
    hourlyRate: "",
    errorCostPerMonth: "",
  })

  const [results, setResults] = useState<{
    timeSaved: number
    costSaved: number
    errorReduction: number
    totalROI: number
  } | null>(null)

  const calculateROI = () => {
    const revenue = Number.parseFloat(inputs.monthlyRevenue) || 0
    const timeSpent = Number.parseFloat(inputs.timeSpentOnBooks) || 0
    const hourlyRate = Number.parseFloat(inputs.hourlyRate) || 0
    const errorCost = Number.parseFloat(inputs.errorCostPerMonth) || 0

    // FlowIQ saves 70% of bookkeeping time
    const timeSaved = timeSpent * 0.7
    const costSaved = timeSaved * hourlyRate

    // Reduce errors by 85%
    const errorReduction = errorCost * 0.85

    const totalROI = (costSaved + errorReduction) * 12 // Annual savings

    setResults({
      timeSaved,
      costSaved,
      errorReduction,
      totalROI,
    })
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold">ROI Calculator</h2>
        <p className="text-muted-foreground text-lg">See how much FlowIQ can save your business in time and money</p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Your Business Details
            </CardTitle>
            <CardDescription>Enter your current business metrics to calculate potential savings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="revenue">Monthly Revenue (R)</Label>
              <Input
                id="revenue"
                type="number"
                placeholder="50000"
                value={inputs.monthlyRevenue}
                onChange={(e) => setInputs((prev) => ({ ...prev, monthlyRevenue: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Hours spent on bookkeeping per week</Label>
              <Input
                id="time"
                type="number"
                placeholder="10"
                value={inputs.timeSpentOnBooks}
                onChange={(e) => setInputs((prev) => ({ ...prev, timeSpentOnBooks: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="rate">Your hourly rate (R)</Label>
              <Input
                id="rate"
                type="number"
                placeholder="500"
                value={inputs.hourlyRate}
                onChange={(e) => setInputs((prev) => ({ ...prev, hourlyRate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="errors">Monthly cost of bookkeeping errors (R)</Label>
              <Input
                id="errors"
                type="number"
                placeholder="2000"
                value={inputs.errorCostPerMonth}
                onChange={(e) => setInputs((prev) => ({ ...prev, errorCostPerMonth: e.target.value }))}
              />
            </div>

            <Button onClick={calculateROI} className="w-full">
              Calculate My Savings
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Your Potential Savings
            </CardTitle>
            <CardDescription>See how FlowIQ transforms your business efficiency</CardDescription>
          </CardHeader>
          <CardContent>
            {results ? (
              <div className="space-y-6">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Time Saved Weekly</span>
                    </div>
                    <span className="font-bold text-green-600">{results.timeSaved.toFixed(1)} hours</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Monthly Cost Savings</span>
                    </div>
                    <span className="font-bold text-blue-600">R{results.costSaved.toLocaleString()}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-950 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium">Error Reduction Savings</span>
                    </div>
                    <span className="font-bold text-purple-600">R{results.errorReduction.toLocaleString()}/month</span>
                  </div>
                </div>

                <Separator />

                <div className="text-center space-y-4">
                  <div className="p-4 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg">
                    <div className="text-2xl font-bold text-primary">R{results.totalROI.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Total Annual Savings</div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">FlowIQ pays for itself in the first month!</p>
                    <Button asChild className="w-full">
                      <Link href="/signup">Start Saving Today - Free Trial</Link>
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Enter your business details to see potential savings</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
