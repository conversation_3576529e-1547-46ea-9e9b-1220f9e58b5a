"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Copy, Check, Key, Lock, Globe, FileJson, Code, RefreshCw, Download } from "lucide-react"

export function ApiDocumentation() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [copiedEndpoint, setCopiedEndpoint] = useState<string | null>(null)
  const [isGeneratingKey, setIsGeneratingKey] = useState(false)

  const copyToClipboard = (text: string, endpoint: string) => {
    navigator.clipboard.writeText(text)
    setCopiedEndpoint(endpoint)

    toast({
      title: "Copied to clipboard",
      description: "The API endpoint has been copied to your clipboard.",
    })

    setTimeout(() => {
      setCopiedEndpoint(null)
    }, 2000)
  }

  const handleGenerateApiKey = () => {
    setIsGeneratingKey(true)

    // TODO: IMPLEMENT PRODUCTION API - Generate API key
    setTimeout(() => {
      setIsGeneratingKey(false)
      toast({
        title: "API Key Generated",
        description: "Your new API key has been generated successfully.",
      })
    }, 1500)
  }

  return (
    <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="authentication">Authentication</TabsTrigger>
        <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
        <TabsTrigger value="examples">Examples</TabsTrigger>
        <TabsTrigger value="sdks">SDKs</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>API Overview</CardTitle>
            <CardDescription>Learn about our RESTful API and how to integrate with it</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="prose dark:prose-invert max-w-none">
              <h3>Introduction</h3>
              <p>
                Our API provides programmatic access to your data and allows you to build custom integrations with your
                existing systems. The API follows RESTful principles and returns responses in JSON format.
              </p>

              <h3>Base URL</h3>
              <div className="bg-muted p-3 rounded-md flex items-center justify-between">
                <code>https://api.saas-starter.co.za/v1</code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard("https://api.saas-starter.co.za/v1", "base-url")}
                >
                  {copiedEndpoint === "base-url" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>

              <h3>Rate Limits</h3>
              <p>The API has rate limits to ensure fair usage and system stability:</p>
              <ul>
                <li>Standard plan: 60 requests per minute</li>
                <li>Professional plan: 120 requests per minute</li>
                <li>Enterprise plan: 300 requests per minute</li>
              </ul>

              <h3>Versioning</h3>
              <p>
                The API is versioned to ensure backward compatibility. The current version is v1. When we make breaking
                changes, we'll release a new version and maintain the old one for a deprecation period.
              </p>

              <h3>Getting Started</h3>
              <ol>
                <li>Generate an API key in the Authentication tab</li>
                <li>Explore available endpoints in the Endpoints tab</li>
                <li>Try out example requests in the Examples tab</li>
                <li>Integrate using our SDKs or direct API calls</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="authentication" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>API Authentication</CardTitle>
            <CardDescription>Secure your API requests with authentication</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="prose dark:prose-invert max-w-none">
              <h3>Authentication Methods</h3>
              <p>All API requests must be authenticated using one of the following methods:</p>

              <h4>API Key Authentication</h4>
              <p>The simplest way to authenticate is using an API key. Include your API key in the request header:</p>
              <div className="bg-muted p-3 rounded-md">
                <code>Authorization: Bearer YOUR_API_KEY</code>
              </div>

              <h4>OAuth 2.0</h4>
              <p>
                For more secure applications, we support OAuth 2.0 authentication. This is recommended for applications
                that act on behalf of users.
              </p>

              <h3>Managing API Keys</h3>
              <p>
                You can generate, view, and revoke API keys below. For security reasons, API keys are only displayed
                once when generated.
              </p>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Your API Keys</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg divide-y">
                    <div className="p-3 flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <Key className="h-4 w-4 text-primary" />
                          <span className="font-medium">Primary API Key</span>
                        </div>
                        <p className="text-xs text-muted-foreground">Created on May 10, 2023</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-sm font-mono bg-muted px-2 py-1 rounded">••••••••••••••••</div>
                        <Button variant="outline" size="sm">
                          Revoke
                        </Button>
                      </div>
                    </div>
                    <div className="p-3 flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <Key className="h-4 w-4 text-primary" />
                          <span className="font-medium">Development API Key</span>
                        </div>
                        <p className="text-xs text-muted-foreground">Created on April 22, 2023</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-sm font-mono bg-muted px-2 py-1 rounded">••••••••••••••••</div>
                        <Button variant="outline" size="sm">
                          Revoke
                        </Button>
                      </div>
                    </div>
                  </div>

                  <Button onClick={handleGenerateApiKey} disabled={isGeneratingKey}>
                    {isGeneratingKey ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Key className="mr-2 h-4 w-4" />
                        Generate New API Key
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <div className="p-4 border rounded-lg bg-amber-50 dark:bg-amber-950/50 text-amber-800 dark:text-amber-200">
              <h3 className="font-medium flex items-center gap-2 mb-2">
                <Lock className="h-5 w-5" />
                Security Best Practices
              </h3>
              <ul className="space-y-2 text-sm">
                <li>Never share your API keys or include them in client-side code</li>
                <li>Rotate your API keys periodically for enhanced security</li>
                <li>Use environment variables to store API keys in your applications</li>
                <li>Set up IP restrictions to limit API access to trusted servers</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="endpoints" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>API Endpoints</CardTitle>
            <CardDescription>Explore available API endpoints and their documentation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Customers</h3>
                <Card>
                  <CardContent className="p-0">
                    <div className="divide-y">
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 text-xs font-medium rounded">
                              GET
                            </span>
                            <code className="font-mono text-sm">/customers</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/customers", "get-customers")
                            }
                          >
                            {copiedEndpoint === "get-customers" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">List all customers with pagination support</p>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 text-xs font-medium rounded">
                              GET
                            </span>
                            <code className="font-mono text-sm">/customers/:id</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/customers/:id", "get-customer")
                            }
                          >
                            {copiedEndpoint === "get-customer" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">Get a specific customer by ID</p>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 text-xs font-medium rounded">
                              POST
                            </span>
                            <code className="font-mono text-sm">/customers</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/customers", "post-customer")
                            }
                          >
                            {copiedEndpoint === "post-customer" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">Create a new customer</p>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 px-2 py-1 text-xs font-medium rounded">
                              PUT
                            </span>
                            <code className="font-mono text-sm">/customers/:id</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/customers/:id", "put-customer")
                            }
                          >
                            {copiedEndpoint === "put-customer" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">Update an existing customer</p>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 px-2 py-1 text-xs font-medium rounded">
                              DELETE
                            </span>
                            <code className="font-mono text-sm">/customers/:id</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/customers/:id", "delete-customer")
                            }
                          >
                            {copiedEndpoint === "delete-customer" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">Delete a customer</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Products</h3>
                <Card>
                  <CardContent className="p-0">
                    <div className="divide-y">
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 text-xs font-medium rounded">
                              GET
                            </span>
                            <code className="font-mono text-sm">/products</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/products", "get-products")
                            }
                          >
                            {copiedEndpoint === "get-products" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">List all products with pagination support</p>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 text-xs font-medium rounded">
                              GET
                            </span>
                            <code className="font-mono text-sm">/products/:id</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/products/:id", "get-product")
                            }
                          >
                            {copiedEndpoint === "get-product" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">Get a specific product by ID</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Transactions</h3>
                <Card>
                  <CardContent className="p-0">
                    <div className="divide-y">
                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 px-2 py-1 text-xs font-medium rounded">
                              GET
                            </span>
                            <code className="font-mono text-sm">/transactions</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/transactions", "get-transactions")
                            }
                          >
                            {copiedEndpoint === "get-transactions" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">List all transactions with pagination support</p>
                      </div>

                      <div className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 text-xs font-medium rounded">
                              POST
                            </span>
                            <code className="font-mono text-sm">/transactions</code>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              copyToClipboard("https://api.saas-starter.co.za/v1/transactions", "post-transaction")
                            }
                          >
                            {copiedEndpoint === "post-transaction" ? (
                              <Check className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground">Create a new transaction</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="flex justify-center">
              <Button variant="outline">
                <FileJson className="mr-2 h-4 w-4" />
                Download OpenAPI Specification
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="examples" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>API Examples</CardTitle>
            <CardDescription>Code examples for common API operations</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Authentication Example</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Select defaultValue="javascript">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="javascript">JavaScript</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="php">PHP</SelectItem>
                        <SelectItem value="curl">cURL</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(
                          `const apiKey = 'YOUR_API_KEY';

fetch('https://api.saas-starter.co.za/v1/customers', {
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`,
                          "auth-example",
                        )
                      }
                    >
                      {copiedEndpoint === "auth-example" ? (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Copied
                        </>
                      ) : (
                        <>
                          <Copy className="mr-2 h-4 w-4" />
                          Copy Code
                        </>
                      )}
                    </Button>
                  </div>
                  <div className="bg-muted p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      <code>{`const apiKey = 'YOUR_API_KEY';

fetch('https://api.saas-starter.co.za/v1/customers', {
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`}</code>
                    </pre>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Create a Customer</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Select defaultValue="javascript">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="javascript">JavaScript</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="php">PHP</SelectItem>
                        <SelectItem value="curl">cURL</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(
                          `const apiKey = 'YOUR_API_KEY';
const customerData = {
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+27 82 123 4567',
  address: {
    street: '123 Main St',
    city: 'Cape Town',
    postalCode: '8001',
    province: 'Western Cape'
  }
};

fetch('https://api.saas-starter.co.za/v1/customers', {
  method: 'POST',
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(customerData)
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`,
                          "create-customer-example",
                        )
                      }
                    >
                      {copiedEndpoint === "create-customer-example" ? (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Copied
                        </>
                      ) : (
                        <>
                          <Copy className="mr-2 h-4 w-4" />
                          Copy Code
                        </>
                      )}
                    </Button>
                  </div>
                  <div className="bg-muted p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      <code>{`const apiKey = 'YOUR_API_KEY';
const customerData = {
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+27 82 123 4567',
  address: {
    street: '123 Main St',
    city: 'Cape Town',
    postalCode: '8001',
    province: 'Western Cape'
  }
};

fetch('https://api.saas-starter.co.za/v1/customers', {
  method: 'POST',
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(customerData)
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`}</code>
                    </pre>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">List Transactions with Filtering</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Select defaultValue="javascript">
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="javascript">JavaScript</SelectItem>
                        <SelectItem value="python">Python</SelectItem>
                        <SelectItem value="php">PHP</SelectItem>
                        <SelectItem value="curl">cURL</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(
                          `const apiKey = 'YOUR_API_KEY';

// Query parameters for filtering
const params = new URLSearchParams({
  status: 'completed',
  from_date: '2023-01-01',
  to_date: '2023-05-31',
  limit: '50',
  page: '1'
});

fetch(\`https://api.saas-starter.co.za/v1/transactions?\${params}\`, {
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`,
                          "list-transactions-example",
                        )
                      }
                    >
                      {copiedEndpoint === "list-transactions-example" ? (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Copied
                        </>
                      ) : (
                        <>
                          <Copy className="mr-2 h-4 w-4" />
                          Copy Code
                        </>
                      )}
                    </Button>
                  </div>
                  <div className="bg-muted p-4 rounded-md overflow-x-auto">
                    <pre className="text-sm">
                      <code>{`const apiKey = 'YOUR_API_KEY';

// Query parameters for filtering
const params = new URLSearchParams({
  status: 'completed',
  from_date: '2023-01-01',
  to_date: '2023-05-31',
  limit: '50',
  page: '1'
});

fetch(\`https://api.saas-starter.co.za/v1/transactions?\${params}\`, {
  headers: {
    'Authorization': \`Bearer \${apiKey}\`,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`}</code>
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="sdks" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>API SDKs & Libraries</CardTitle>
            <CardDescription>Official client libraries to simplify API integration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="cursor-pointer hover:border-primary transition-colors">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <Code className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">JavaScript SDK</h3>
                    <p className="text-sm text-muted-foreground">For Node.js and browser applications</p>
                    <div className="flex gap-2 mt-2">
                      <Button variant="outline" size="sm">
                        <Globe className="mr-2 h-4 w-4" />
                        Docs
                      </Button>
                      <Button size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        npm
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:border-primary transition-colors">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <Code className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Python SDK</h3>
                    <p className="text-sm text-muted-foreground">For Python applications and scripts</p>
                    <div className="flex gap-2 mt-2">
                      <Button variant="outline" size="sm">
                        <Globe className="mr-2 h-4 w-4" />
                        Docs
                      </Button>
                      <Button size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        pip
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="cursor-pointer hover:border-primary transition-colors">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <Code className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">PHP SDK</h3>
                    <p className="text-sm text-muted-foreground">For PHP applications and websites</p>
                    <div className="flex gap-2 mt-2">
                      <Button variant="outline" size="sm">
                        <Globe className="mr-2 h-4 w-4" />
                        Docs
                      </Button>
                      <Button size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Composer
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="prose dark:prose-invert max-w-none">
              <h3>SDK Installation</h3>

              <h4>JavaScript</h4>
              <div className="bg-muted p-3 rounded-md">
                <code>npm install @saas-starter/api-client</code>
              </div>
              <p>Basic usage:</p>
              <div className="bg-muted p-3 rounded-md">
                <pre className="text-sm">
                  <code>{`import { SaasClient } from '@saas-starter/api-client';

const client = new SaasClient('YOUR_API_KEY');

// List customers
const customers = await client.customers.list();

// Create a customer
const newCustomer = await client.customers.create({
  name: 'John Doe',
  email: '<EMAIL>'
});`}</code>
                </pre>
              </div>

              <h4>Python</h4>
              <div className="bg-muted p-3 rounded-md">
                <code>pip install saas-starter-client</code>
              </div>
              <p>Basic usage:</p>
              <div className="bg-muted p-3 rounded-md">
                <pre className="text-sm">
                  <code>{`from saas_starter import Client

client = Client(api_key='YOUR_API_KEY')

# List customers
customers = client.customers.list()

# Create a customer
new_customer = client.customers.create(
    name='John Doe',
    email='<EMAIL>'
)`}</code>
                </pre>
              </div>

              <h4>PHP</h4>
              <div className="bg-muted p-3 rounded-md">
                <code>composer require saas-starter/api-client</code>
              </div>
              <p>Basic usage:</p>
              <div className="bg-muted p-3 rounded-md">
                <pre className="text-sm">
                  <code>{`<?php
require_once 'vendor/autoload.php';

$client = new SaasStarter\\Client('YOUR_API_KEY');

// List customers
$customers = $client->customers->list();

// Create a customer
$newCustomer = $client->customers->create([
    'name' => 'John Doe',
    'email' => '<EMAIL>'
]);`}</code>
                </pre>
              </div>
            </div>

            <div className="p-4 border rounded-lg bg-muted/50">
              <h3 className="font-medium mb-2">Community Libraries</h3>
              <p className="text-sm text-muted-foreground mb-4">
                These libraries are maintained by the community and are not officially supported.
              </p>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Ruby Client</p>
                    <p className="text-xs text-muted-foreground">By @developer123</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Globe className="h-4 w-4 mr-2" />
                    GitHub
                  </Button>
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Go Client</p>
                    <p className="text-xs text-muted-foreground">By @gopher456</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Globe className="h-4 w-4 mr-2" />
                    GitHub
                  </Button>
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Java Client</p>
                    <p className="text-xs text-muted-foreground">By @javadev789</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Globe className="h-4 w-4 mr-2" />
                    GitHub
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
