"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Upload, FileText, Database, Table, FileUp, FileDown, AlertTriangle } from "lucide-react"

export function DataManagement() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("import")
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [importType, setImportType] = useState("csv")
  const [exportType, setExportType] = useState("csv")
  const [exportDataset, setExportDataset] = useState("customers")

  const handleImport = (event: React.FormEvent) => {
    event.preventDefault()
    // TODO: IMPLEMENT PRODUCTION API - Handle data import
    toast({
      title: "Import started",
      description: "Your data import has been started. You'll be notified when it's complete.",
    })
    setIsImportDialogOpen(false)
  }

  const handleExport = (event: React.FormEvent) => {
    event.preventDefault()
    // TODO: IMPLEMENT PRODUCTION API - Handle data export
    toast({
      title: "Export started",
      description: "Your data export has been started. You'll be notified when it's ready to download.",
    })
    setIsExportDialogOpen(false)
  }

  return (
    <>
      <Tabs defaultValue="import" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="import">Import Data</TabsTrigger>
          <TabsTrigger value="export">Export Data</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Import Data</CardTitle>
              <CardDescription>Import your data from various file formats into the system</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => {
                    setImportType("csv")
                    setIsImportDialogOpen(true)
                  }}
                >
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <FileText className="h-8 w-8 text-primary" />
                      <h3 className="font-medium">CSV Import</h3>
                      <p className="text-sm text-muted-foreground">Import data from CSV files</p>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => {
                    setImportType("excel")
                    setIsImportDialogOpen(true)
                  }}
                >
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <Table className="h-8 w-8 text-primary" />
                      <h3 className="font-medium">Excel Import</h3>
                      <p className="text-sm text-muted-foreground">Import data from Excel spreadsheets</p>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => {
                    setImportType("json")
                    setIsImportDialogOpen(true)
                  }}
                >
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <Database className="h-8 w-8 text-primary" />
                      <h3 className="font-medium">JSON Import</h3>
                      <p className="text-sm text-muted-foreground">Import data from JSON files</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="border rounded-lg p-4 bg-muted/50">
                <h3 className="font-medium flex items-center gap-2 mb-2">
                  <FileUp className="h-4 w-4" />
                  Import History
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 bg-background rounded">
                    <div>
                      <p className="font-medium">customers_may_2023.csv</p>
                      <p className="text-xs text-muted-foreground">Imported on May 15, 2023</p>
                    </div>
                    <div className="text-sm text-green-500">Completed</div>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded">
                    <div>
                      <p className="font-medium">products_update.xlsx</p>
                      <p className="text-xs text-muted-foreground">Imported on April 28, 2023</p>
                    </div>
                    <div className="text-sm text-green-500">Completed</div>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded">
                    <div>
                      <p className="font-medium">transactions_q1.json</p>
                      <p className="text-xs text-muted-foreground">Imported on March 31, 2023</p>
                    </div>
                    <div className="text-sm text-destructive">Failed</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="export" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Export Data</CardTitle>
              <CardDescription>Export your data to various file formats</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => {
                    setExportType("csv")
                    setIsExportDialogOpen(true)
                  }}
                >
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <FileText className="h-8 w-8 text-primary" />
                      <h3 className="font-medium">CSV Export</h3>
                      <p className="text-sm text-muted-foreground">Export data to CSV files</p>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => {
                    setExportType("excel")
                    setIsExportDialogOpen(true)
                  }}
                >
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <Table className="h-8 w-8 text-primary" />
                      <h3 className="font-medium">Excel Export</h3>
                      <p className="text-sm text-muted-foreground">Export data to Excel spreadsheets</p>
                    </div>
                  </CardContent>
                </Card>

                <Card
                  className="cursor-pointer hover:border-primary transition-colors"
                  onClick={() => {
                    setExportType("json")
                    setIsExportDialogOpen(true)
                  }}
                >
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <Database className="h-8 w-8 text-primary" />
                      <h3 className="font-medium">JSON Export</h3>
                      <p className="text-sm text-muted-foreground">Export data to JSON files</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="border rounded-lg p-4 bg-muted/50">
                <h3 className="font-medium flex items-center gap-2 mb-2">
                  <FileDown className="h-4 w-4" />
                  Export History
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 bg-background rounded">
                    <div>
                      <p className="font-medium">customers_export_may_2023.csv</p>
                      <p className="text-xs text-muted-foreground">Exported on May 20, 2023</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      Download
                    </Button>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded">
                    <div>
                      <p className="font-medium">financial_report_q1.xlsx</p>
                      <p className="text-xs text-muted-foreground">Exported on April 2, 2023</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      Download
                    </Button>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-background rounded">
                    <div>
                      <p className="font-medium">inventory_snapshot.json</p>
                      <p className="text-xs text-muted-foreground">Exported on March 15, 2023</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      Download
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Data</DialogTitle>
            <DialogDescription>Upload a file to import data into the system.</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleImport}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="import-type">File Type</Label>
                <Select defaultValue={importType} onValueChange={setImportType}>
                  <SelectTrigger id="import-type">
                    <SelectValue placeholder="Select file type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV File</SelectItem>
                    <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                    <SelectItem value="json">JSON File</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="import-target">Import To</Label>
                <Select defaultValue="customers">
                  <SelectTrigger id="import-target">
                    <SelectValue placeholder="Select target" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customers">Customers</SelectItem>
                    <SelectItem value="products">Products</SelectItem>
                    <SelectItem value="transactions">Transactions</SelectItem>
                    <SelectItem value="inventory">Inventory</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="file-upload">Upload File</Label>
                <div className="border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center">
                  <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-sm font-medium">Drag and drop your file here</p>
                  <p className="text-xs text-muted-foreground mb-4">or</p>
                  <Button type="button" variant="outline" size="sm">
                    Browse Files
                  </Button>
                  <input
                    id="file-upload"
                    type="file"
                    className="hidden"
                    accept={importType === "csv" ? ".csv" : importType === "excel" ? ".xlsx,.xls" : ".json"}
                  />
                  <p className="text-xs text-muted-foreground mt-4">Maximum file size: 10MB</p>
                </div>
              </div>

              <div className="flex items-start gap-2 p-3 bg-amber-50 dark:bg-amber-950/50 text-amber-800 dark:text-amber-200 rounded-lg">
                <AlertTriangle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium">Important</p>
                  <p>Importing data will merge with existing records. Duplicate records will be updated.</p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsImportDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Start Import</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Data</DialogTitle>
            <DialogDescription>Configure your data export settings.</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleExport}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="export-type">File Type</Label>
                <Select defaultValue={exportType} onValueChange={setExportType}>
                  <SelectTrigger id="export-type">
                    <SelectValue placeholder="Select file type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV File</SelectItem>
                    <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                    <SelectItem value="json">JSON File</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="export-dataset">Dataset</Label>
                <Select defaultValue={exportDataset} onValueChange={setExportDataset}>
                  <SelectTrigger id="export-dataset">
                    <SelectValue placeholder="Select dataset" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customers">Customers</SelectItem>
                    <SelectItem value="products">Products</SelectItem>
                    <SelectItem value="transactions">Transactions</SelectItem>
                    <SelectItem value="inventory">Inventory</SelectItem>
                    <SelectItem value="all">All Data</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date-range">Date Range</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date" className="text-xs">
                      Start Date
                    </Label>
                    <Input id="start-date" type="date" />
                  </div>
                  <div>
                    <Label htmlFor="end-date" className="text-xs">
                      End Date
                    </Label>
                    <Input id="end-date" type="date" />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="export-options">Export Options</Label>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="include-headers" className="rounded border-gray-300" defaultChecked />
                  <Label htmlFor="include-headers" className="text-sm font-normal">
                    Include column headers
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="include-deleted" className="rounded border-gray-300" />
                  <Label htmlFor="include-deleted" className="text-sm font-normal">
                    Include deleted records
                  </Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsExportDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Start Export</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  )
}
