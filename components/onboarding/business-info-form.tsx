"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

const businessInfoSchema = z.object({
  businessName: z.string().min(2, "Business name is required"),
  businessType: z.string().min(1, "Business type is required"),
  registrationNumber: z.string().min(1, "Registration number is required"),
  address: z.string().min(5, "Address is required"),
  city: z.string().min(2, "City is required"),
  postalCode: z.string().min(4, "Postal code is required"),
  province: z.string().min(2, "Province is required"),
})

type BusinessInfoValues = z.infer<typeof businessInfoSchema>

export function BusinessInfoForm() {
  const { toast } = useToast()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<BusinessInfoValues>({
    resolver: zodResolver(businessInfoSchema),
    defaultValues: {
      businessName: "",
      businessType: "",
      registrationNumber: "",
      address: "",
      city: "",
      postalCode: "",
      province: "",
    },
  })

  async function onSubmit(data: BusinessInfoValues) {
    setIsSubmitting(true)

    try {
      // TODO: IMPLEMENT PRODUCTION API - Save business info
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Business information saved",
        description: "Your business information has been saved successfully.",
      })

      router.push("/onboarding/preferences")
    } catch (error) {
      toast({
        title: "An error occurred",
        description: "Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="businessName">Business Name</Label>
            <Input id="businessName" {...form.register("businessName")} />
            {form.formState.errors.businessName && (
              <p className="text-sm text-destructive">{form.formState.errors.businessName.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="businessType">Business Type</Label>
            <Select
              onValueChange={(value) => form.setValue("businessType", value)}
              defaultValue={form.getValues("businessType")}
            >
              <SelectTrigger id="businessType">
                <SelectValue placeholder="Select business type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sole_proprietor">Sole Proprietor</SelectItem>
                <SelectItem value="partnership">Partnership</SelectItem>
                <SelectItem value="pty_ltd">Pty Ltd</SelectItem>
                <SelectItem value="close_corporation">Close Corporation</SelectItem>
                <SelectItem value="non_profit">Non-Profit</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.businessType && (
              <p className="text-sm text-destructive">{form.formState.errors.businessType.message}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="registrationNumber">Registration Number</Label>
          <Input id="registrationNumber" {...form.register("registrationNumber")} placeholder="e.g. 2020/123456/07" />
          {form.formState.errors.registrationNumber && (
            <p className="text-sm text-destructive">{form.formState.errors.registrationNumber.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="address">Street Address</Label>
          <Input id="address" {...form.register("address")} />
          {form.formState.errors.address && (
            <p className="text-sm text-destructive">{form.formState.errors.address.message}</p>
          )}
        </div>

        <div className="grid gap-4 sm:grid-cols-3">
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input id="city" {...form.register("city")} />
            {form.formState.errors.city && (
              <p className="text-sm text-destructive">{form.formState.errors.city.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="postalCode">Postal Code</Label>
            <Input id="postalCode" {...form.register("postalCode")} />
            {form.formState.errors.postalCode && (
              <p className="text-sm text-destructive">{form.formState.errors.postalCode.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="province">Province</Label>
            <Select
              onValueChange={(value) => form.setValue("province", value)}
              defaultValue={form.getValues("province")}
            >
              <SelectTrigger id="province">
                <SelectValue placeholder="Select province" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="eastern_cape">Eastern Cape</SelectItem>
                <SelectItem value="free_state">Free State</SelectItem>
                <SelectItem value="gauteng">Gauteng</SelectItem>
                <SelectItem value="kwazulu_natal">KwaZulu-Natal</SelectItem>
                <SelectItem value="limpopo">Limpopo</SelectItem>
                <SelectItem value="mpumalanga">Mpumalanga</SelectItem>
                <SelectItem value="north_west">North West</SelectItem>
                <SelectItem value="northern_cape">Northern Cape</SelectItem>
                <SelectItem value="western_cape">Western Cape</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.province && (
              <p className="text-sm text-destructive">{form.formState.errors.province.message}</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Next Step"}
        </Button>
      </div>
    </form>
  )
}
