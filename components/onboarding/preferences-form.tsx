"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

const preferencesSchema = z.object({
  theme: z.enum(["light", "dark", "system"]),
  currency: z.string().min(1, "Currency is required"),
  notifications: z.object({
    email: z.boolean(),
    push: z.boolean(),
    sms: z.boolean(),
  }),
  language: z.string().min(1, "Language is required"),
})

type PreferencesValues = z.infer<typeof preferencesSchema>

export function PreferencesForm() {
  const { toast } = useToast()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<PreferencesValues>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      theme: "system",
      currency: "ZAR",
      notifications: {
        email: true,
        push: true,
        sms: false,
      },
      language: "en",
    },
  })

  async function onSubmit(data: PreferencesValues) {
    setIsSubmitting(true)

    try {
      // TODO: IMPLEMENT PRODUCTION API - Save preferences
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Preferences saved",
        description: "Your preferences have been saved successfully.",
      })

      router.push("/onboarding/tax-details")
    } catch (error) {
      toast({
        title: "An error occurred",
        description: "Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Theme Preference</Label>
          <RadioGroup
            defaultValue={form.getValues("theme")}
            onValueChange={(value) => form.setValue("theme", value as "light" | "dark" | "system")}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="light" id="theme-light" />
              <Label htmlFor="theme-light">Light</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="dark" id="theme-dark" />
              <Label htmlFor="theme-dark">Dark</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="system" id="theme-system" />
              <Label htmlFor="theme-system">System</Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-2">
          <Label htmlFor="currency">Default Currency</Label>
          <Select onValueChange={(value) => form.setValue("currency", value)} defaultValue={form.getValues("currency")}>
            <SelectTrigger id="currency">
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ZAR">South African Rand (ZAR)</SelectItem>
              <SelectItem value="USD">US Dollar (USD)</SelectItem>
              <SelectItem value="EUR">Euro (EUR)</SelectItem>
              <SelectItem value="GBP">British Pound (GBP)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="language">Language</Label>
          <Select onValueChange={(value) => form.setValue("language", value)} defaultValue={form.getValues("language")}>
            <SelectTrigger id="language">
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="af">Afrikaans</SelectItem>
              <SelectItem value="zu">Zulu</SelectItem>
              <SelectItem value="xh">Xhosa</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Notification Preferences</Label>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="email-notifications" className="flex-1">
                Email Notifications
              </Label>
              <Switch
                id="email-notifications"
                checked={form.getValues("notifications.email")}
                onCheckedChange={(checked) => form.setValue("notifications.email", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="push-notifications" className="flex-1">
                Push Notifications
              </Label>
              <Switch
                id="push-notifications"
                checked={form.getValues("notifications.push")}
                onCheckedChange={(checked) => form.setValue("notifications.push", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="sms-notifications" className="flex-1">
                SMS Notifications
              </Label>
              <Switch
                id="sms-notifications"
                checked={form.getValues("notifications.sms")}
                onCheckedChange={(checked) => form.setValue("notifications.sms", checked)}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={() => router.push("/onboarding/business-info")}>
          Previous Step
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Next Step"}
        </Button>
      </div>
    </form>
  )
}
