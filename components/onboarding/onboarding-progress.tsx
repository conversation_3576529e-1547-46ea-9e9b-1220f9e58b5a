"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { Check } from "lucide-react"

export function OnboardingProgress() {
  const pathname = usePathname()

  const steps = [
    {
      id: "business-info",
      name: "Business Information",
      href: "/onboarding/business-info",
    },
    {
      id: "preferences",
      name: "Preferences",
      href: "/onboarding/preferences",
    },
    {
      id: "tax-details",
      name: "Tax Details",
      href: "/onboarding/tax-details",
    },
  ]

  const currentStepIndex = steps.findIndex((step) => pathname?.includes(step.id))

  return (
    <div className="space-y-4">
      <nav aria-label="Progress">
        <ol role="list" className="space-y-3 md:flex md:space-x-6 md:space-y-0">
          {steps.map((step, index) => {
            const isActive = pathname?.includes(step.id)
            const isCompleted = index < currentStepIndex

            return (
              <li key={step.id} className="md:flex-1">
                <Link
                  href={step.href}
                  className={`group flex flex-col border-l-4 py-2 pl-4 md:border-l-0 md:border-t-4 md:pb-0 md:pl-0 md:pt-4 ${
                    isActive
                      ? "border-primary text-primary"
                      : isCompleted
                        ? "border-primary text-primary"
                        : "border-muted-foreground/20 text-muted-foreground hover:border-muted-foreground/60"
                  }`}
                >
                  <span className="flex items-center text-sm font-medium">
                    <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full border border-current">
                      {isCompleted ? <Check className="h-4 w-4" /> : <span>{index + 1}</span>}
                    </span>
                    <span className="ml-2">{step.name}</span>
                  </span>
                </Link>
              </li>
            )
          })}
        </ol>
      </nav>
    </div>
  )
}
