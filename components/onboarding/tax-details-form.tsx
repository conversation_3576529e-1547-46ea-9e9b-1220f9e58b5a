"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import { saVatValidator } from "@/lib/validators/sa-validators"

const taxDetailsSchema = z.object({
  vatRegistered: z.boolean(),
  vatNumber: z
    .string()
    .optional()
    .refine((val) => !val || saVatValidator.test(val), {
      message: "Invalid South African VAT number format",
    }),
  taxNumber: z.string().min(10, "Tax number must be at least 10 characters"),
  taxPeriod: z.enum(["monthly", "bimonthly", "yearly"]),
  payeRegistered: z.boolean(),
  payeNumber: z.string().optional(),
})

type TaxDetailsValues = z.infer<typeof taxDetailsSchema>

export function TaxDetailsForm() {
  const { toast } = useToast()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<TaxDetailsValues>({
    resolver: zodResolver(taxDetailsSchema),
    defaultValues: {
      vatRegistered: false,
      vatNumber: "",
      taxNumber: "",
      taxPeriod: "monthly",
      payeRegistered: false,
      payeNumber: "",
    },
  })

  const vatRegistered = form.watch("vatRegistered")
  const payeRegistered = form.watch("payeRegistered")

  async function onSubmit(data: TaxDetailsValues) {
    setIsSubmitting(true)

    try {
      // TODO: IMPLEMENT PRODUCTION API - Save tax details
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // TODO: IMPLEMENT SA TAX CALCULATOR - Initialize tax calculator with user data

      toast({
        title: "Tax details saved",
        description: "Your tax information has been saved successfully.",
      })

      router.push("/dashboard")
    } catch (error) {
      toast({
        title: "An error occurred",
        description: "Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="vat-registered" className="flex-1">
              VAT Registered
            </Label>
            <Switch
              id="vat-registered"
              checked={vatRegistered}
              onCheckedChange={(checked) => {
                form.setValue("vatRegistered", checked)
                if (!checked) {
                  form.setValue("vatNumber", "")
                }
              }}
            />
          </div>

          {vatRegistered && (
            <div className="space-y-2 mt-4">
              <Label htmlFor="vatNumber">VAT Number</Label>
              <Input id="vatNumber" {...form.register("vatNumber")} placeholder="e.g. 4220159380" />
              {form.formState.errors.vatNumber && (
                <p className="text-sm text-destructive">{form.formState.errors.vatNumber.message}</p>
              )}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="taxNumber">Tax Number</Label>
          <Input id="taxNumber" {...form.register("taxNumber")} placeholder="e.g. 7259687219" />
          {form.formState.errors.taxNumber && (
            <p className="text-sm text-destructive">{form.formState.errors.taxNumber.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label>Tax Period</Label>
          <RadioGroup
            defaultValue={form.getValues("taxPeriod")}
            onValueChange={(value) => form.setValue("taxPeriod", value as "monthly" | "bimonthly" | "yearly")}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="monthly" id="tax-monthly" />
              <Label htmlFor="tax-monthly">Monthly</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="bimonthly" id="tax-bimonthly" />
              <Label htmlFor="tax-bimonthly">Bi-monthly</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yearly" id="tax-yearly" />
              <Label htmlFor="tax-yearly">Yearly</Label>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="paye-registered" className="flex-1">
              PAYE Registered
            </Label>
            <Switch
              id="paye-registered"
              checked={payeRegistered}
              onCheckedChange={(checked) => {
                form.setValue("payeRegistered", checked)
                if (!checked) {
                  form.setValue("payeNumber", "")
                }
              }}
            />
          </div>

          {payeRegistered && (
            <div className="space-y-2 mt-4">
              <Label htmlFor="payeNumber">PAYE Number</Label>
              <Input id="payeNumber" {...form.register("payeNumber")} placeholder="e.g. 7259687219" />
              {form.formState.errors.payeNumber && (
                <p className="text-sm text-destructive">{form.formState.errors.payeNumber.message}</p>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={() => router.push("/onboarding/preferences")}>
          Previous Step
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Completing Setup..." : "Complete Setup"}
        </Button>
      </div>
    </form>
  )
}
