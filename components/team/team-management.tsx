"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { MoreHorizontal, Mail, UserPlus, UserMinus, User<PERSON><PERSON>, Clock } from "lucide-react"
import type { TeamMember, Role } from "@/lib/types/team"

export function TeamManagement() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(true)
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null)

  useEffect(() => {
    async function fetchTeamMembers() {
      try {
        // TODO: IMPLEMENT PRODUCTION API - Fetch team members
        await new Promise((resolve) => setTimeout(resolve, 1000))

        const mockTeamMembers: TeamMember[] = [
          {
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
            role: "owner",
            avatar: "/placeholder.svg?height=40&width=40",
            joinedAt: "2023-01-15",
            lastActive: "2023-05-22T10:30:00Z",
          },
          {
            id: "2",
            name: "Jane Smith",
            email: "<EMAIL>",
            role: "admin",
            avatar: "/placeholder.svg?height=40&width=40",
            joinedAt: "2023-02-20",
            lastActive: "2023-05-21T14:45:00Z",
          },
          {
            id: "3",
            name: "Michael Johnson",
            email: "<EMAIL>",
            role: "member",
            avatar: "/placeholder.svg?height=40&width=40",
            joinedAt: "2023-03-10",
            lastActive: "2023-05-20T09:15:00Z",
          },
          {
            id: "4",
            name: "Sarah Williams",
            email: "<EMAIL>",
            role: "viewer",
            avatar: "/placeholder.svg?height=40&width=40",
            joinedAt: "2023-04-05",
            lastActive: "2023-05-19T16:30:00Z",
          },
        ]

        setTeamMembers(mockTeamMembers)
      } catch (error) {
        console.error("Failed to fetch team members:", error)
        toast({
          title: "Error",
          description: "Failed to load team members. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchTeamMembers()
  }, [toast])

  const handleInviteMember = (event: React.FormEvent) => {
    event.preventDefault()
    // TODO: IMPLEMENT PRODUCTION API - Send invitation
    toast({
      title: "Invitation sent",
      description: "The team member has been invited successfully.",
    })
    setIsInviteDialogOpen(false)
  }

  const handleEditMember = (event: React.FormEvent) => {
    event.preventDefault()
    if (!selectedMember) return

    // TODO: IMPLEMENT PRODUCTION API - Update team member
    setTeamMembers((members) => members.map((member) => (member.id === selectedMember.id ? selectedMember : member)))

    toast({
      title: "Member updated",
      description: "The team member has been updated successfully.",
    })
    setIsEditDialogOpen(false)
  }

  const handleRemoveMember = (memberId: string) => {
    // TODO: IMPLEMENT PRODUCTION API - Remove team member
    setTeamMembers((members) => members.filter((member) => member.id !== memberId))

    toast({
      title: "Member removed",
      description: "The team member has been removed successfully.",
    })
  }

  const getRoleBadgeVariant = (role: Role) => {
    switch (role) {
      case "owner":
        return "default"
      case "admin":
        return "secondary"
      case "member":
        return "outline"
      case "viewer":
        return "destructive"
      default:
        return "outline"
    }
  }

  const formatLastActive = (lastActive?: string) => {
    if (!lastActive) return "Never"

    const date = new Date(lastActive)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours} hours ago`
    if (diffInHours < 48) return "Yesterday"
    return date.toLocaleDateString()
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>Loading team members...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg animate-pulse">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 rounded-full bg-muted"></div>
                  <div className="space-y-2">
                    <div className="h-4 w-32 bg-muted rounded"></div>
                    <div className="h-3 w-40 bg-muted rounded"></div>
                  </div>
                </div>
                <div className="h-6 w-16 bg-muted rounded-full"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>Manage your team members and their access levels</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {teamMembers.map((member) => (
              <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarImage src={member.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {member.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.name}</p>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{formatLastActive(member.lastActive)}</span>
                  </div>
                  <Badge variant={getRoleBadgeVariant(member.role)}>
                    {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Actions</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedMember(member)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <UserCog className="mr-2 h-4 w-4" />
                        Edit Role
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          window.location.href = `mailto:${member.email}`
                        }}
                      >
                        <Mail className="mr-2 h-4 w-4" />
                        Send Email
                      </DropdownMenuItem>
                      {member.role !== "owner" && (
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive"
                          onClick={() => handleRemoveMember(member.id)}
                        >
                          <UserMinus className="mr-2 h-4 w-4" />
                          Remove
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>

          <Button variant="outline" className="mt-6 w-full" onClick={() => setIsInviteDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Invite New Member
          </Button>
        </CardContent>
      </Card>

      <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
            <DialogDescription>
              Send an invitation to join your team. They'll receive an email with instructions.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleInviteMember}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" placeholder="<EMAIL>" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select defaultValue="member">
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="message">Personal Message (Optional)</Label>
                <Input id="message" placeholder="I'd like to invite you to our team..." />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Send Invitation</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Team Member</DialogTitle>
            <DialogDescription>Update the role and permissions for this team member.</DialogDescription>
          </DialogHeader>
          {selectedMember && (
            <form onSubmit={handleEditMember}>
              <div className="space-y-4 py-4">
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarImage src={selectedMember.avatar || "/placeholder.svg"} />
                    <AvatarFallback>
                      {selectedMember.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{selectedMember.name}</p>
                    <p className="text-sm text-muted-foreground">{selectedMember.email}</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-role">Role</Label>
                  <Select
                    defaultValue={selectedMember.role}
                    onValueChange={(value) => {
                      setSelectedMember({
                        ...selectedMember,
                        role: value as Role,
                      })
                    }}
                    disabled={selectedMember.role === "owner"}
                  >
                    <SelectTrigger id="edit-role">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="member">Member</SelectItem>
                      <SelectItem value="viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                  {selectedMember.role === "owner" && (
                    <p className="text-xs text-muted-foreground mt-1">
                      The owner role cannot be changed. Transfer ownership first to change this user's role.
                    </p>
                  )}
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={selectedMember.role === "owner"}>
                  Save Changes
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
