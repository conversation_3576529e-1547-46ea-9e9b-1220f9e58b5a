"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertTriangle, Package, Plus } from "lucide-react"
import type { Product, InventoryLevel } from "@/lib/types/product"

interface InventoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: Product | null
}

export function InventoryDialog({ open, onOpenChange, product }: InventoryDialogProps) {
  const [inventory, setInventory] = useState<InventoryLevel[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (open && product) {
      fetchInventory()
    }
  }, [open, product])

  const fetchInventory = async () => {
    if (!product) return

    setLoading(true)
    try {
      const response = await fetch(`/api/products/${product.id}/inventory`)
      const data = await response.json()
      setInventory(data)
    } catch (error) {
      console.error("Error fetching inventory:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatQuantity = (quantity: number) => {
    return new Intl.NumberFormat().format(quantity)
  }

  if (!product) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Inventory - {product.name}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="text-sm font-medium text-muted-foreground">SKU</div>
              <div className="text-lg font-mono">{product.sku}</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="text-sm font-medium text-muted-foreground">Barcode</div>
              <div className="text-lg font-mono">{product.barcode}</div>
            </div>
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="text-sm font-medium text-muted-foreground">Low Stock Threshold</div>
              <div className="text-lg">{formatQuantity(product.lowStockThreshold)}</div>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-8">Loading inventory...</div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Location</TableHead>
                    <TableHead>On Hand</TableHead>
                    <TableHead>Reserved</TableHead>
                    <TableHead>Available</TableHead>
                    <TableHead>Reorder Point</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {inventory.map((inv) => (
                    <TableRow key={inv.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{inv.location?.name}</div>
                          <div className="text-sm text-muted-foreground">{inv.location?.code}</div>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono">{formatQuantity(inv.quantityOnHand)}</TableCell>
                      <TableCell className="font-mono">{formatQuantity(inv.quantityReserved)}</TableCell>
                      <TableCell className="font-mono">{formatQuantity(inv.quantityAvailable)}</TableCell>
                      <TableCell className="font-mono">{formatQuantity(inv.reorderPoint)}</TableCell>
                      <TableCell>
                        {inv.quantityAvailable <= inv.reorderPoint ? (
                          <Badge variant="destructive" className="flex items-center space-x-1">
                            <AlertTriangle className="h-3 w-3" />
                            <span>Low Stock</span>
                          </Badge>
                        ) : (
                          <Badge variant="default">In Stock</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">
                          <Plus className="h-4 w-4 mr-1" />
                          Adjust
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {inventory.length === 0 && !loading && (
            <div className="text-center py-8 text-muted-foreground">No inventory records found for this product</div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
