"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { BarChart3, LineChart, PieChart, FileText, Download, Calendar, Filter, RefreshCw } from "lucide-react"

export function ReportsOverview() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("financial")
  const [isGenerating, setIsGenerating] = useState(false)

  const handleGenerateReport = (reportType: string) => {
    setIsGenerating(true)

    // TODO: IMPLEMENT PRODUCTION API - Generate report
    setTimeout(() => {
      setIsGenerating(false)
      toast({
        title: "Report Generated",
        description: `Your ${reportType} report has been generated successfully.`,
      })
    }, 2000)
  }

  return (
    <Tabs defaultValue="financial" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="financial">Financial</TabsTrigger>
        <TabsTrigger value="sales">Sales</TabsTrigger>
        <TabsTrigger value="tax">Tax</TabsTrigger>
        <TabsTrigger value="custom">Custom</TabsTrigger>
      </TabsList>

      <TabsContent value="financial" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Financial Reports</CardTitle>
            <CardDescription>Generate comprehensive financial reports for your business</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("income statement")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <BarChart3 className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Income Statement</h3>
                    <p className="text-sm text-muted-foreground">Revenue, expenses, and profit</p>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("balance sheet")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <LineChart className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Balance Sheet</h3>
                    <p className="text-sm text-muted-foreground">Assets, liabilities, and equity</p>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("cash flow")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <PieChart className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Cash Flow</h3>
                    <p className="text-sm text-muted-foreground">Cash inflows and outflows</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Report Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="date-range">Date Range</Label>
                      <Select defaultValue="current-month">
                        <SelectTrigger id="date-range">
                          <SelectValue placeholder="Select date range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="current-month">Current Month</SelectItem>
                          <SelectItem value="previous-month">Previous Month</SelectItem>
                          <SelectItem value="current-quarter">Current Quarter</SelectItem>
                          <SelectItem value="previous-quarter">Previous Quarter</SelectItem>
                          <SelectItem value="year-to-date">Year to Date</SelectItem>
                          <SelectItem value="previous-year">Previous Year</SelectItem>
                          <SelectItem value="custom">Custom Range</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="format">Format</Label>
                      <Select defaultValue="pdf">
                        <SelectTrigger id="format">
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pdf">PDF</SelectItem>
                          <SelectItem value="excel">Excel</SelectItem>
                          <SelectItem value="csv">CSV</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button disabled={isGenerating}>
                      {isGenerating ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate Report
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="border rounded-lg p-4 bg-muted/50">
              <h3 className="font-medium flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4" />
                Recent Reports
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Income Statement - May 2023</p>
                    <p className="text-xs text-muted-foreground">Generated on May 31, 2023</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Balance Sheet - Q1 2023</p>
                    <p className="text-xs text-muted-foreground">Generated on April 2, 2023</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Cash Flow - Q1 2023</p>
                    <p className="text-xs text-muted-foreground">Generated on April 2, 2023</p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="sales" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Sales Reports</CardTitle>
            <CardDescription>Analyze your sales performance and trends</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("sales summary")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <BarChart3 className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Sales Summary</h3>
                    <p className="text-sm text-muted-foreground">Overview of all sales</p>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("product performance")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <LineChart className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Product Performance</h3>
                    <p className="text-sm text-muted-foreground">Sales by product</p>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("customer analysis")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <PieChart className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Customer Analysis</h3>
                    <p className="text-sm text-muted-foreground">Sales by customer segment</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Report Filters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="date-range">Time Period</Label>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label htmlFor="start-date" className="text-xs">
                            From
                          </Label>
                          <Input id="start-date" type="date" />
                        </div>
                        <div>
                          <Label htmlFor="end-date" className="text-xs">
                            To
                          </Label>
                          <Input id="end-date" type="date" />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="product-category">Product Category</Label>
                      <Select defaultValue="all">
                        <SelectTrigger id="product-category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="electronics">Electronics</SelectItem>
                          <SelectItem value="clothing">Clothing</SelectItem>
                          <SelectItem value="home">Home & Garden</SelectItem>
                          <SelectItem value="food">Food & Beverage</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sales-channel">Sales Channel</Label>
                      <Select defaultValue="all">
                        <SelectTrigger id="sales-channel">
                          <SelectValue placeholder="Select channel" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Channels</SelectItem>
                          <SelectItem value="online">Online Store</SelectItem>
                          <SelectItem value="retail">Retail Locations</SelectItem>
                          <SelectItem value="wholesale">Wholesale</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button variant="outline">
                      <Filter className="mr-2 h-4 w-4" />
                      Apply Filters
                    </Button>
                    <Button disabled={isGenerating}>
                      {isGenerating ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate Report
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="tax" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Tax Reports</CardTitle>
            <CardDescription>Generate South African tax reports and compliance documents</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("vat return")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <FileText className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">VAT Return</h3>
                    <p className="text-sm text-muted-foreground">VAT 201 preparation</p>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("paye")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <FileText className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">PAYE Report</h3>
                    <p className="text-sm text-muted-foreground">Employee tax withholding</p>
                  </div>
                </CardContent>
              </Card>

              <Card
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleGenerateReport("annual tax")}
              >
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <Calendar className="h-8 w-8 text-primary" />
                    <h3 className="font-medium">Annual Tax Summary</h3>
                    <p className="text-sm text-muted-foreground">Year-end tax preparation</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Tax Period</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="tax-period">Tax Period</Label>
                      <Select defaultValue="current">
                        <SelectTrigger id="tax-period">
                          <SelectValue placeholder="Select period" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="current">Current Period</SelectItem>
                          <SelectItem value="previous">Previous Period</SelectItem>
                          <SelectItem value="year-to-date">Year to Date</SelectItem>
                          <SelectItem value="custom">Custom Period</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="tax-type">Tax Type</Label>
                      <Select defaultValue="vat">
                        <SelectTrigger id="tax-type">
                          <SelectValue placeholder="Select tax type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="vat">VAT</SelectItem>
                          <SelectItem value="paye">PAYE</SelectItem>
                          <SelectItem value="income">Income Tax</SelectItem>
                          <SelectItem value="all">All Taxes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button disabled={isGenerating}>
                      {isGenerating ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate Tax Report
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="p-4 border rounded-lg bg-amber-50 dark:bg-amber-950/50 text-amber-800 dark:text-amber-200">
              <h3 className="font-medium mb-2">Tax Compliance Notice</h3>
              <p className="text-sm">
                These reports are designed to help with tax preparation but should be reviewed by a qualified accountant
                before submission to SARS. Tax regulations change frequently, and we recommend consulting with a tax
                professional.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="custom" className="space-y-4 mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Custom Reports</CardTitle>
            <CardDescription>Build and save custom reports tailored to your business needs</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Report Builder</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="report-name">Report Name</Label>
                    <Input id="report-name" placeholder="Enter a name for your custom report" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-source">Data Source</Label>
                    <Select defaultValue="sales">
                      <SelectTrigger id="data-source">
                        <SelectValue placeholder="Select data source" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sales">Sales Data</SelectItem>
                        <SelectItem value="customers">Customer Data</SelectItem>
                        <SelectItem value="inventory">Inventory Data</SelectItem>
                        <SelectItem value="financial">Financial Data</SelectItem>
                        <SelectItem value="tax">Tax Data</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Fields to Include</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="field-date" className="rounded border-gray-300" defaultChecked />
                        <Label htmlFor="field-date" className="text-sm font-normal">
                          Date
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="field-amount" className="rounded border-gray-300" defaultChecked />
                        <Label htmlFor="field-amount" className="text-sm font-normal">
                          Amount
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="field-customer" className="rounded border-gray-300" defaultChecked />
                        <Label htmlFor="field-customer" className="text-sm font-normal">
                          Customer
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="field-product" className="rounded border-gray-300" defaultChecked />
                        <Label htmlFor="field-product" className="text-sm font-normal">
                          Product
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="field-category" className="rounded border-gray-300" />
                        <Label htmlFor="field-category" className="text-sm font-normal">
                          Category
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="field-payment" className="rounded border-gray-300" />
                        <Label htmlFor="field-payment" className="text-sm font-normal">
                          Payment Method
                        </Label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Grouping & Sorting</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="group-by" className="text-xs">
                          Group By
                        </Label>
                        <Select defaultValue="none">
                          <SelectTrigger id="group-by">
                            <SelectValue placeholder="Select field" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">No Grouping</SelectItem>
                            <SelectItem value="date">Date</SelectItem>
                            <SelectItem value="customer">Customer</SelectItem>
                            <SelectItem value="product">Product</SelectItem>
                            <SelectItem value="category">Category</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="sort-by" className="text-xs">
                          Sort By
                        </Label>
                        <Select defaultValue="date-desc">
                          <SelectTrigger id="sort-by">
                            <SelectValue placeholder="Select field" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="date-desc">Date (Newest First)</SelectItem>
                            <SelectItem value="date-asc">Date (Oldest First)</SelectItem>
                            <SelectItem value="amount-desc">Amount (Highest First)</SelectItem>
                            <SelectItem value="amount-asc">Amount (Lowest First)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button variant="outline">Save Template</Button>
                    <Button disabled={isGenerating}>
                      {isGenerating ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <FileText className="mr-2 h-4 w-4" />
                          Generate Report
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="border rounded-lg p-4 bg-muted/50">
              <h3 className="font-medium flex items-center gap-2 mb-2">
                <FileText className="h-4 w-4" />
                Saved Report Templates
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Monthly Sales by Product</p>
                    <p className="text-xs text-muted-foreground">Created on April 15, 2023</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                    <Button variant="ghost" size="sm">
                      Run
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Customer Spending Analysis</p>
                    <p className="text-xs text-muted-foreground">Created on March 22, 2023</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                    <Button variant="ghost" size="sm">
                      Run
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-2 bg-background rounded">
                  <div>
                    <p className="font-medium">Quarterly Tax Summary</p>
                    <p className="text-xs text-muted-foreground">Created on February 10, 2023</p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                    <Button variant="ghost" size="sm">
                      Run
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
