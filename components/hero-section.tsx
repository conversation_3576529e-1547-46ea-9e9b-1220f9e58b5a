import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

export function HeroSection() {
  return (
    <section className="container px-4 md:px-6 py-12 md:py-24 lg:py-32">
      <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
        <div className="flex flex-col justify-center space-y-4">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
              South Africa&apos;s Premier SaaS Solution
            </h1>
            <p className="max-w-[600px] text-muted-foreground md:text-xl">
              Streamline your business operations with our comprehensive platform designed specifically for South
              African businesses.
            </p>
          </div>
          <div className="flex flex-col gap-2 min-[400px]:flex-row">
            <Link href="/signup">
              <Button size="lg">Get Started</Button>
            </Link>
            <Link href="/#features">
              <Button variant="outline" size="lg">
                Learn More
              </Button>
            </Link>
          </div>
        </div>
        <Image
          src="/placeholder.svg?height=550&width=550"
          width={550}
          height={550}
          alt="Hero"
          className="mx-auto aspect-video overflow-hidden rounded-xl object-cover sm:w-full lg:aspect-square"
          priority
        />
      </div>
    </section>
  )
}
