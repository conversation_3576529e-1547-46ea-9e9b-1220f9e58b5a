"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { User, Settings, Shield, Bell, Building } from "lucide-react"
import { cn } from "@/lib/utils"

const settingsNavigation = [
  {
    name: "Profile",
    href: "/settings/profile",
    icon: User,
    description: "Personal information",
  },
  {
    name: "Account",
    href: "/settings/account",
    icon: Settings,
    description: "Account preferences",
  },
  {
    name: "Security",
    href: "/settings/security",
    icon: Shield,
    description: "Password & security",
  },
  {
    name: "Notifications",
    href: "/settings/notifications",
    icon: Bell,
    description: "Email & push notifications",
  },
  {
    name: "Business",
    href: "/settings/business",
    icon: Building,
    description: "Business information",
  },
]

export function SettingsSidebar() {
  const pathname = usePathname()

  return (
    <Card>
      <CardContent className="p-4">
        <nav className="space-y-2">
          {settingsNavigation.map((item) => {
            const isActive = pathname === item.href
            const Icon = item.icon

            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground",
                )}
              >
                <Icon className="h-4 w-4" />
                <div>
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs opacity-70">{item.description}</div>
                </div>
              </Link>
            )
          })}
        </nav>
      </CardContent>
    </Card>
  )
}
