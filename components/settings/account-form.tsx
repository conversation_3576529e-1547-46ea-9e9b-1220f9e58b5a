"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

const accountFormSchema = z.object({
  language: z.string().min(1, "Language is required"),
  timezone: z.string().min(1, "Timezone is required"),
  currency: z.string().min(1, "Currency is required"),
  emailVerified: z.boolean(),
  twoFactorEnabled: z.boolean(),
})

type AccountFormValues = z.infer<typeof accountFormSchema>

export function AccountForm() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      language: "en",
      timezone: "Africa/Johannesburg",
      currency: "ZAR",
      emailVerified: false,
      twoFactorEnabled: false,
    },
  })

  async function onSubmit(data: AccountFormValues) {
    setIsLoading(true)

    try {
      // TODO: IMPLEMENT PRODUCTION API - Update account settings
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Account settings updated",
        description: "Your account settings have been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update account settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const emailVerified = form.watch("emailVerified")

  return (
    <div className="space-y-6">
      {!emailVerified && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your email address is not verified. Please check your inbox for a verification email.
            <Button variant="link" className="h-auto p-0 ml-1">
              Resend verification email
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Regional Settings</CardTitle>
          <CardDescription>Configure your regional preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="language">Language</Label>
                <Select
                  onValueChange={(value) => form.setValue("language", value)}
                  defaultValue={form.getValues("language")}
                >
                  <SelectTrigger id="language">
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="af">Afrikaans</SelectItem>
                    <SelectItem value="zu">Zulu</SelectItem>
                    <SelectItem value="xh">Xhosa</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <Select
                  onValueChange={(value) => form.setValue("timezone", value)}
                  defaultValue={form.getValues("timezone")}
                >
                  <SelectTrigger id="timezone">
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Africa/Johannesburg">South Africa Standard Time</SelectItem>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="Europe/London">London</SelectItem>
                    <SelectItem value="America/New_York">New York</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Default Currency</Label>
              <Select
                onValueChange={(value) => form.setValue("currency", value)}
                defaultValue={form.getValues("currency")}
              >
                <SelectTrigger id="currency">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ZAR">South African Rand (ZAR)</SelectItem>
                  <SelectItem value="USD">US Dollar (USD)</SelectItem>
                  <SelectItem value="EUR">Euro (EUR)</SelectItem>
                  <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Account Security</CardTitle>
          <CardDescription>Manage your account security settings</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-verified">Email Verification</Label>
              <p className="text-sm text-muted-foreground">
                {emailVerified ? "Your email is verified" : "Verify your email address"}
              </p>
            </div>
            <Switch
              id="email-verified"
              checked={emailVerified}
              onCheckedChange={(checked) => form.setValue("emailVerified", checked)}
              disabled
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="two-factor">Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">Add an extra layer of security to your account</p>
            </div>
            <Switch
              id="two-factor"
              checked={form.watch("twoFactorEnabled")}
              onCheckedChange={(checked) => {
                form.setValue("twoFactorEnabled", checked)
                // TODO: IMPLEMENT 2FA - Add two-factor authentication setup
                toast({
                  title: "Feature coming soon",
                  description: "Two-factor authentication will be available soon.",
                })
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
