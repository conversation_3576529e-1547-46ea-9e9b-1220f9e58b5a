"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { saVatValidator } from "@/lib/validators/sa-validators"

const businessFormSchema = z.object({
  businessName: z.string().min(2, "Business name is required"),
  businessType: z.string().min(1, "Business type is required"),
  registrationNumber: z.string().min(1, "Registration number is required"),
  vatRegistered: z.boolean(),
  vatNumber: z
    .string()
    .optional()
    .refine((val) => !val || saVatValidator.test(val), {
      message: "Invalid South African VAT number format",
    }),
  taxNumber: z.string().min(10, "Tax number must be at least 10 characters"),
  address: z.string().min(5, "Address is required"),
  city: z.string().min(2, "City is required"),
  postalCode: z.string().min(4, "Postal code is required"),
  province: z.string().min(2, "Province is required"),
})

type BusinessFormValues = z.infer<typeof businessFormSchema>

export function BusinessForm() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<BusinessFormValues>({
    resolver: zodResolver(businessFormSchema),
    defaultValues: {
      businessName: "",
      businessType: "",
      registrationNumber: "",
      vatRegistered: false,
      vatNumber: "",
      taxNumber: "",
      address: "",
      city: "",
      postalCode: "",
      province: "",
    },
  })

  const vatRegistered = form.watch("vatRegistered")

  async function onSubmit(data: BusinessFormValues) {
    setIsLoading(true)

    try {
      // TODO: IMPLEMENT PRODUCTION API - Update business information
      // TODO: INTEGRATE SARS API - Validate VAT and tax numbers
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Business information updated",
        description: "Your business details have been updated successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update business information. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Business Information</CardTitle>
          <CardDescription>Update your business details and registration information</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name</Label>
                <Input id="businessName" {...form.register("businessName")} />
                {form.formState.errors.businessName && (
                  <p className="text-sm text-destructive">{form.formState.errors.businessName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessType">Business Type</Label>
                <Select
                  onValueChange={(value) => form.setValue("businessType", value)}
                  defaultValue={form.getValues("businessType")}
                >
                  <SelectTrigger id="businessType">
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sole_proprietor">Sole Proprietor</SelectItem>
                    <SelectItem value="partnership">Partnership</SelectItem>
                    <SelectItem value="pty_ltd">Pty Ltd</SelectItem>
                    <SelectItem value="close_corporation">Close Corporation</SelectItem>
                    <SelectItem value="non_profit">Non-Profit</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.businessType && (
                  <p className="text-sm text-destructive">{form.formState.errors.businessType.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="registrationNumber">Registration Number</Label>
              <Input
                id="registrationNumber"
                {...form.register("registrationNumber")}
                placeholder="e.g. 2020/123456/07"
              />
              {form.formState.errors.registrationNumber && (
                <p className="text-sm text-destructive">{form.formState.errors.registrationNumber.message}</p>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="vat-registered">VAT Registered</Label>
                  <p className="text-sm text-muted-foreground">Is your business registered for VAT?</p>
                </div>
                <Switch
                  id="vat-registered"
                  checked={vatRegistered}
                  onCheckedChange={(checked) => {
                    form.setValue("vatRegistered", checked)
                    if (!checked) {
                      form.setValue("vatNumber", "")
                    }
                  }}
                />
              </div>

              {vatRegistered && (
                <div className="space-y-2">
                  <Label htmlFor="vatNumber">VAT Number</Label>
                  <Input id="vatNumber" {...form.register("vatNumber")} placeholder="e.g. 4220159380" />
                  {form.formState.errors.vatNumber && (
                    <p className="text-sm text-destructive">{form.formState.errors.vatNumber.message}</p>
                  )}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="taxNumber">Tax Number</Label>
              <Input id="taxNumber" {...form.register("taxNumber")} placeholder="e.g. 7259687219" />
              {form.formState.errors.taxNumber && (
                <p className="text-sm text-destructive">{form.formState.errors.taxNumber.message}</p>
              )}
            </div>

            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : "Save Business Information"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Business Address</CardTitle>
          <CardDescription>Update your business address information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="address">Street Address</Label>
              <Input id="address" {...form.register("address")} />
              {form.formState.errors.address && (
                <p className="text-sm text-destructive">{form.formState.errors.address.message}</p>
              )}
            </div>

            <div className="grid gap-4 sm:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input id="city" {...form.register("city")} />
                {form.formState.errors.city && (
                  <p className="text-sm text-destructive">{form.formState.errors.city.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input id="postalCode" {...form.register("postalCode")} />
                {form.formState.errors.postalCode && (
                  <p className="text-sm text-destructive">{form.formState.errors.postalCode.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="province">Province</Label>
                <Select
                  onValueChange={(value) => form.setValue("province", value)}
                  defaultValue={form.getValues("province")}
                >
                  <SelectTrigger id="province">
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="eastern_cape">Eastern Cape</SelectItem>
                    <SelectItem value="free_state">Free State</SelectItem>
                    <SelectItem value="gauteng">Gauteng</SelectItem>
                    <SelectItem value="kwazulu_natal">KwaZulu-Natal</SelectItem>
                    <SelectItem value="limpopo">Limpopo</SelectItem>
                    <SelectItem value="mpumalanga">Mpumalanga</SelectItem>
                    <SelectItem value="north_west">North West</SelectItem>
                    <SelectItem value="northern_cape">Northern Cape</SelectItem>
                    <SelectItem value="western_cape">Western Cape</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.province && (
                  <p className="text-sm text-destructive">{form.formState.errors.province.message}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
