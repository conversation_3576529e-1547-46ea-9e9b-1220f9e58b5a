"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"

const notificationsFormSchema = z.object({
  email: z.object({
    marketing: z.boolean(),
    security: z.boolean(),
    updates: z.boolean(),
    billing: z.boolean(),
    reports: z.boolean(),
  }),
  push: z.object({
    enabled: z.boolean(),
    marketing: z.boolean(),
    security: z.boolean(),
    updates: z.boolean(),
  }),
  sms: z.object({
    enabled: z.boolean(),
    security: z.boolean(),
    billing: z.boolean(),
  }),
})

type NotificationsFormValues = z.infer<typeof notificationsFormSchema>

export function NotificationsForm() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<NotificationsFormValues>({
    resolver: zodResolver(notificationsFormSchema),
    defaultValues: {
      email: {
        marketing: false,
        security: true,
        updates: true,
        billing: true,
        reports: false,
      },
      push: {
        enabled: true,
        marketing: false,
        security: true,
        updates: true,
      },
      sms: {
        enabled: false,
        security: false,
        billing: false,
      },
    },
  })

  async function onSubmit(data: NotificationsFormValues) {
    setIsLoading(true)

    try {
      // TODO: IMPLEMENT PRODUCTION API - Update notification preferences
      // TODO: INTEGRATE EMAIL SERVICE - Configure email notifications
      // TODO: INTEGRATE PUSH SERVICE - Configure push notifications
      // TODO: INTEGRATE SMS SERVICE - Configure SMS notifications
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "Notification preferences updated",
        description: "Your notification settings have been saved successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update notification preferences. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const pushEnabled = form.watch("push.enabled")
  const smsEnabled = form.watch("sms.enabled")

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Email Notifications</CardTitle>
          <CardDescription>Choose what email notifications you'd like to receive</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-marketing">Marketing emails</Label>
              <p className="text-sm text-muted-foreground">Receive emails about new features and promotions</p>
            </div>
            <Switch
              id="email-marketing"
              checked={form.watch("email.marketing")}
              onCheckedChange={(checked) => form.setValue("email.marketing", checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-security">Security alerts</Label>
              <p className="text-sm text-muted-foreground">Receive alerts about account security</p>
            </div>
            <Switch
              id="email-security"
              checked={form.watch("email.security")}
              onCheckedChange={(checked) => form.setValue("email.security", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-updates">Product updates</Label>
              <p className="text-sm text-muted-foreground">Receive emails about product updates and changes</p>
            </div>
            <Switch
              id="email-updates"
              checked={form.watch("email.updates")}
              onCheckedChange={(checked) => form.setValue("email.updates", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-billing">Billing notifications</Label>
              <p className="text-sm text-muted-foreground">Receive emails about billing and payments</p>
            </div>
            <Switch
              id="email-billing"
              checked={form.watch("email.billing")}
              onCheckedChange={(checked) => form.setValue("email.billing", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-reports">Weekly reports</Label>
              <p className="text-sm text-muted-foreground">Receive weekly summary reports</p>
            </div>
            <Switch
              id="email-reports"
              checked={form.watch("email.reports")}
              onCheckedChange={(checked) => form.setValue("email.reports", checked)}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Push Notifications</CardTitle>
          <CardDescription>Manage push notifications to your devices</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="push-enabled">Enable push notifications</Label>
              <p className="text-sm text-muted-foreground">Allow push notifications to your devices</p>
            </div>
            <Switch
              id="push-enabled"
              checked={pushEnabled}
              onCheckedChange={(checked) => form.setValue("push.enabled", checked)}
            />
          </div>

          {pushEnabled && (
            <>
              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-marketing">Marketing notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive push notifications about promotions</p>
                </div>
                <Switch
                  id="push-marketing"
                  checked={form.watch("push.marketing")}
                  onCheckedChange={(checked) => form.setValue("push.marketing", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-security">Security alerts</Label>
                  <p className="text-sm text-muted-foreground">Receive push notifications about security</p>
                </div>
                <Switch
                  id="push-security"
                  checked={form.watch("push.security")}
                  onCheckedChange={(checked) => form.setValue("push.security", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="push-updates">Product updates</Label>
                  <p className="text-sm text-muted-foreground">Receive push notifications about updates</p>
                </div>
                <Switch
                  id="push-updates"
                  checked={form.watch("push.updates")}
                  onCheckedChange={(checked) => form.setValue("push.updates", checked)}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>SMS Notifications</CardTitle>
          <CardDescription>Manage SMS notifications to your phone</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="sms-enabled">Enable SMS notifications</Label>
              <p className="text-sm text-muted-foreground">Allow SMS notifications to your phone</p>
            </div>
            <Switch
              id="sms-enabled"
              checked={smsEnabled}
              onCheckedChange={(checked) => form.setValue("sms.enabled", checked)}
            />
          </div>

          {smsEnabled && (
            <>
              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sms-security">Security alerts</Label>
                  <p className="text-sm text-muted-foreground">Receive SMS alerts about account security</p>
                </div>
                <Switch
                  id="sms-security"
                  checked={form.watch("sms.security")}
                  onCheckedChange={(checked) => form.setValue("sms.security", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sms-billing">Billing notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive SMS notifications about billing</p>
                </div>
                <Switch
                  id="sms-billing"
                  checked={form.watch("sms.billing")}
                  onCheckedChange={(checked) => form.setValue("sms.billing", checked)}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <Button onClick={form.handleSubmit(onSubmit)} disabled={isLoading}>
        {isLoading ? "Saving..." : "Save Notification Preferences"}
      </Button>
    </div>
  )
}
