"use client"

import { usePermissionsStore } from "@/lib/stores/permissions-store"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react"
import Link from "next/link"

interface UsageLimitsProps {
  currentProducts?: number
  currentTransactions?: number
  currentTeamMembers?: number
}

export function UsageLimits({
  currentProducts = 0,
  currentTransactions = 0,
  currentTeamMembers = 0,
}: UsageLimitsProps) {
  const { permissions } = usePermissionsStore()

  const isUnlimited = (limit: number) => limit === -1
  const getUsagePercentage = (current: number, max: number) => {
    if (isUnlimited(max)) return 0
    return Math.min((current / max) * 100, 100)
  }

  const isNearLimit = (current: number, max: number) => {
    if (isUnlimited(max)) return false
    return current >= max * 0.8
  }

  const isAtLimit = (current: number, max: number) => {
    if (isUnlimited(max)) return false
    return current >= max
  }

  const limits = [
    {
      name: "Products",
      current: currentProducts,
      max: permissions.maxProducts,
      description: "products in your inventory",
    },
    {
      name: "Transactions",
      current: currentTransactions,
      max: permissions.maxTransactions,
      description: "transactions per month",
    },
    {
      name: "Team Members",
      current: currentTeamMembers,
      max: permissions.maxTeamMembers,
      description: "team members",
    },
  ]

  const hasLimits = limits.some((limit) => !isUnlimited(limit.max))

  if (!hasLimits) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Usage & Limits</CardTitle>
        <CardDescription>Track your current usage against your plan limits</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {limits.map((limit) => {
          if (isUnlimited(limit.max)) return null

          const percentage = getUsagePercentage(limit.current, limit.max)
          const nearLimit = isNearLimit(limit.current, limit.max)
          const atLimit = isAtLimit(limit.current, limit.max)

          return (
            <div key={limit.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{limit.name}</span>
                  {(nearLimit || atLimit) && <AlertTriangle className="h-4 w-4 text-amber-500" />}
                </div>
                <span className="text-sm text-muted-foreground">
                  {limit.current} / {limit.max}
                </span>
              </div>
              <Progress
                value={percentage}
                className={`h-2 ${atLimit ? "bg-red-100" : nearLimit ? "bg-amber-100" : ""}`}
              />
              <p className="text-xs text-muted-foreground">
                {limit.current} of {limit.max} {limit.description} used
              </p>
              {atLimit && (
                <div className="mt-2 p-2 bg-red-50 dark:bg-red-950 rounded-md">
                  <p className="text-xs text-red-700 dark:text-red-300 mb-2">
                    You've reached your {limit.name.toLowerCase()} limit. Upgrade to add more.
                  </p>
                  <Button asChild size="sm" variant="outline">
                    <Link href="/dashboard/billing">
                      Upgrade Plan
                      <ArrowRight className="ml-2 h-3 w-3" />
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          )
        })}
      </CardContent>
    </Card>
  )
}
