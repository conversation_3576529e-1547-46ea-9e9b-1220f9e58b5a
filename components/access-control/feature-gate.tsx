"use client"

import type React from "react"
import { useEffect, useState } from "react"
import type { UserPermissions } from "@/lib/types/permissions"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Lock, ArrowRight, Sparkles } from "lucide-react"
import Link from "next/link"

interface FeatureGateProps {
  feature: keyof UserPermissions["features"]
  children: React.ReactNode
  fallback?: React.ReactNode
  showUpgradePrompt?: boolean
}

export function FeatureGate({ feature, children, fallback, showUpgradePrompt = true }: FeatureGateProps) {
  const [hasAccess, setHasAccess] = useState(false)
  const [upgradeInfo, setUpgradeInfo] = useState({ message: "", action: "" })
  const [userLevel, setUserLevel] = useState<UserPermissions["level"]>("visitor")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simple client-side permission check without store to avoid circular dependencies
    const checkPermissions = () => {
      try {
        // Default to visitor level for safety
        const level = "visitor" as UserPermissions["level"]

        // Simple feature access logic
        const publicFeatures = ["publicContent", "demoMode", "roiCalculator"]
        const registeredFeatures = [
          "basicDashboard",
          "limitedProductEntry",
          "sampleTransactions",
          "basicOnboarding",
          "languageSettings",
        ]
        const onboardedFeatures = [
          "fullDashboard",
          "realTimeAnalytics",
          "aiInsights",
          "teamManagement",
          "fullReporting",
          "dataExport",
          "integrations",
          "offlineSync",
        ]

        let access = false
        if (publicFeatures.includes(feature)) {
          access = true
        } else if (registeredFeatures.includes(feature)) {
          access = level !== "visitor"
        } else if (onboardedFeatures.includes(feature)) {
          access = level === "onboarded"
        }

        setHasAccess(access)
        setUserLevel(level)
        setUpgradeInfo({
          message: getUpgradeMessage(feature, level),
          action: getNextAction(feature, level),
        })
        setIsLoading(false)
      } catch (error) {
        console.error("Permission check error:", error)
        setHasAccess(false)
        setIsLoading(false)
      }
    }

    checkPermissions()
  }, [feature])

  if (isLoading) {
    return <div className="animate-pulse bg-muted h-32 rounded-md" />
  }

  if (hasAccess) {
    return <>{children}</>
  }

  if (fallback) {
    return <>{fallback}</>
  }

  if (!showUpgradePrompt) {
    return null
  }

  return (
    <Card className="border-dashed border-2 border-muted-foreground/20">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <Lock className="h-6 w-6 text-primary" />
        </div>
        <CardTitle className="text-lg">Feature Locked</CardTitle>
        <CardDescription className="text-sm">{upgradeInfo.message}</CardDescription>
      </CardHeader>
      <CardContent className="text-center">
        <UpgradeButton level={userLevel} action={upgradeInfo.action} feature={feature} />
      </CardContent>
    </Card>
  )
}

function UpgradeButton({
  level,
  action,
  feature,
}: {
  level: UserPermissions["level"]
  action: string
  feature: keyof UserPermissions["features"]
}) {
  if (level === "visitor") {
    return (
      <Button asChild className="w-full">
        <Link href="/signup">
          <Sparkles className="mr-2 h-4 w-4" />
          {action}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </Button>
    )
  }

  if (level === "registered") {
    return (
      <Button asChild className="w-full">
        <Link href="/onboarding/business-info">
          <Sparkles className="mr-2 h-4 w-4" />
          {action}
          <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </Button>
    )
  }

  return (
    <Button asChild className="w-full">
      <Link href="/dashboard/billing">
        <Sparkles className="mr-2 h-4 w-4" />
        {action}
        <ArrowRight className="ml-2 h-4 w-4" />
      </Link>
    </Button>
  )
}

function getUpgradeMessage(feature: keyof UserPermissions["features"], currentLevel: UserPermissions["level"]): string {
  const messages = {
    fullDashboard: "Complete your setup to unlock your full business dashboard with real-time insights!",
    realTimeAnalytics: "Finish onboarding to see live analytics and AI-powered business insights.",
    aiInsights: "Get AI-powered forecasting and recommendations by completing your business setup.",
    teamManagement: "Add team members and manage permissions after completing onboarding.",
    fullReporting: "Access comprehensive reports and data exports with full account setup.",
    dataExport: "Export your data in multiple formats after completing onboarding.",
    integrations: "Connect with payment systems and bank feeds after setup completion.",
    apiAccess: "Upgrade to Growth plan for full API access and custom integrations.",
    whatsappIntegration: "Upgrade to Starter plan or higher for WhatsApp Business integration.",
    customBranding: "Upgrade to Growth plan for custom branding and white-label options.",
    prioritySupport: "Upgrade to Growth plan for priority support and dedicated assistance.",
    advancedReporting: "Upgrade to Growth plan for advanced reporting and analytics.",
  }

  return messages[feature] || "Complete your setup to unlock this feature."
}

function getNextAction(feature: keyof UserPermissions["features"], currentLevel: UserPermissions["level"]): string {
  if (currentLevel === "visitor") {
    return "Sign up for free to get started"
  }
  if (currentLevel === "registered") {
    return "Complete setup to unlock"
  }
  return "Upgrade plan to access"
}
