"use client"

import { usePermissionsStore } from "@/lib/stores/permissions-store"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Eye, ArrowRight } from "lucide-react"
import Link from "next/link"

interface DemoModeBannerProps {
  demoType?: "product" | "transaction" | "dashboard"
}

export function DemoModeBanner({ demoType = "dashboard" }: DemoModeBannerProps) {
  const { permissions } = usePermissionsStore()

  if (permissions.level !== "visitor") {
    return null
  }

  const getDemoMessage = () => {
    switch (demoType) {
      case "product":
        return "You're viewing a demo product. Sign up to add your own products and track inventory!"
      case "transaction":
        return "This is sample transaction data. Create an account to record your real business transactions!"
      case "dashboard":
        return "You're viewing a demo dashboard. Sign up to see your actual business metrics and insights!"
      default:
        return "You're in demo mode. Sign up to access your personalized business dashboard!"
    }
  }

  return (
    <Alert className="mb-4 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
      <Eye className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <span className="text-sm">{getDemoMessage()}</span>
        <Button asChild size="sm" variant="outline" className="ml-4">
          <Link href="/signup">
            Start Free
            <ArrowRight className="ml-2 h-3 w-3" />
          </Link>
        </Button>
      </AlertDescription>
    </Alert>
  )
}
