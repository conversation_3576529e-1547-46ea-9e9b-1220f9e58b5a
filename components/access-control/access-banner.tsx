"use client"

import { useState, useEffect } from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON>rk<PERSON>, ArrowRight } from "lucide-react"
import Link from "next/link"

export function AccessBanner() {
  const [userLevel, setUserLevel] = useState<"visitor" | "registered" | "onboarded">("visitor")
  const [onboardingStep, setOnboardingStep] = useState(0)

  useEffect(() => {
    // Simple client-side check without store dependencies
    const level = "visitor" // Default to visitor for safety
    const step = 0

    setUserLevel(level)
    setOnboardingStep(step)
  }, [])

  if (userLevel === "onboarded") {
    return null // No banner for fully onboarded users
  }

  const getBannerContent = () => {
    switch (userLevel) {
      case "visitor":
        return {
          title: "🚀 Start Your Business Journey",
          description: "Sign up for free to access your business dashboard and start tracking your success!",
          action: "Get Started Free",
          href: "/signup",
        }
      case "registered":
        return {
          title: "⚡ Almost There! Complete Your Setup",
          description: `You're ${onboardingStep}/3 steps complete. Unlock AI insights and real-time analytics!`,
          action: "Continue Setup",
          href: "/onboarding/business-info",
        }
      default:
        return null
    }
  }

  const content = getBannerContent()
  if (!content) return null

  return (
    <Alert className="mb-6 border-primary/20 bg-primary/5">
      <Sparkles className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex-1">
          <div className="font-medium text-sm mb-1">{content.title}</div>
          <div className="text-xs text-muted-foreground mb-2">{content.description}</div>
          {userLevel === "registered" && (
            <Progress value={(onboardingStep / 3) * 100} className="h-2 w-full max-w-xs" />
          )}
        </div>
        <Button asChild size="sm" className="ml-4">
          <Link href={content.href}>
            {content.action}
            <ArrowRight className="ml-2 h-3 w-3" />
          </Link>
        </Button>
      </AlertDescription>
    </Alert>
  )
}
