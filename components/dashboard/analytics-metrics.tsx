"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Users, DollarSign, ShoppingCart, Activity } from "lucide-react"

interface AnalyticsMetric {
  id: string
  title: string
  value: string
  change: string
  trend: "up" | "down"
  icon: React.ComponentType<{ className?: string }>
}

export function AnalyticsMetrics() {
  const [metrics, setMetrics] = useState<AnalyticsMetric[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchMetrics() {
      try {
        // TODO: IMPLEMENT PRODUCTION API - Fetch real analytics data
        await new Promise((resolve) => setTimeout(resolve, 1500))

        const mockMetrics: AnalyticsMetric[] = [
          {
            id: "revenue",
            title: "Total Revenue",
            value: "R 125,430",
            change: "+12.5%",
            trend: "up",
            icon: DollarSign,
          },
          {
            id: "users",
            title: "Active Users",
            value: "2,847",
            change: "+8.2%",
            trend: "up",
            icon: Users,
          },
          {
            id: "orders",
            title: "Total Orders",
            value: "1,234",
            change: "-2.1%",
            trend: "down",
            icon: ShoppingCart,
          },
          {
            id: "conversion",
            title: "Conversion Rate",
            value: "3.24%",
            change: "+0.8%",
            trend: "up",
            icon: Activity,
          },
        ]

        setMetrics(mockMetrics)
      } catch (error) {
        console.error("Failed to fetch analytics metrics:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchMetrics()
  }, [])

  if (isLoading) {
    return null
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => {
        const Icon = metric.icon
        const TrendIcon = metric.trend === "up" ? TrendingUp : TrendingDown

        return (
          <Card key={metric.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className={`flex items-center text-xs ${metric.trend === "up" ? "text-green-500" : "text-red-500"}`}>
                <TrendIcon className="mr-1 h-3 w-3" />
                {metric.change} from last month
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
