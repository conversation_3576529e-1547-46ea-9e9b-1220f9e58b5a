import type React from "react"
import { ModeToggle } from "@/components/mode-toggle"
import { But<PERSON> } from "@/components/ui/button"
import { PlusCircle } from "lucide-react"

interface DashboardHeaderProps {
  title: string
  description?: string
  action?: React.ReactNode
}

export function DashboardHeader({ title, description, action }: DashboardHeaderProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {description && <p className="text-muted-foreground">{description}</p>}
      </div>
      <div className="flex items-center gap-2">
        {action || (
          <Button size="sm">
            <PlusCircle className="mr-2 h-4 w-4" />
            New Item
          </Button>
        )}
        <ModeToggle />
      </div>
    </div>
  )
}
