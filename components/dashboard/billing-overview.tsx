"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { CreditCard, Calendar, Plus } from "lucide-react"

interface BillingData {
  currentPlan: {
    name: string
    price: string
    status: "active" | "cancelled" | "past_due"
    nextBilling: string
  }
  usage: {
    users: { current: number; limit: number }
    storage: { current: string; limit: string }
    apiCalls: { current: number; limit: number }
  }
  invoices: Array<{
    id: string
    date: string
    amount: string
    status: "paid" | "pending" | "failed"
  }>
}

export function BillingOverview() {
  const [billingData, setBillingData] = useState<BillingData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchBillingData() {
      try {
        // TODO: IMPLEMENT PRODUCTION API - Fetch real billing data
        // TODO: INTEGRATE STRIPE - Connect to <PERSON><PERSON> for subscription management
        await new Promise((resolve) => setTimeout(resolve, 1500))

        const mockData: BillingData = {
          currentPlan: {
            name: "Professional",
            price: "R 599/month",
            status: "active",
            nextBilling: "2024-02-15",
          },
          usage: {
            users: { current: 8, limit: 20 },
            storage: { current: "2.4 GB", limit: "10 GB" },
            apiCalls: { current: 15420, limit: 50000 },
          },
          invoices: [
            { id: "INV-001", date: "2024-01-15", amount: "R 599.00", status: "paid" },
            { id: "INV-002", date: "2023-12-15", amount: "R 599.00", status: "paid" },
            { id: "INV-003", date: "2023-11-15", amount: "R 599.00", status: "paid" },
          ],
        }

        setBillingData(mockData)
      } catch (error) {
        console.error("Failed to fetch billing data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchBillingData()
  }, [])

  if (isLoading) {
    return null
  }

  if (!billingData) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Failed to load billing information</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Current Plan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{billingData.currentPlan.name}</h3>
                <p className="text-2xl font-bold">{billingData.currentPlan.price}</p>
              </div>
              <Badge variant={billingData.currentPlan.status === "active" ? "default" : "destructive"}>
                {billingData.currentPlan.status}
              </Badge>
            </div>

            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              Next billing: {billingData.currentPlan.nextBilling}
            </div>

            <div className="flex gap-2">
              <Button size="sm">Upgrade Plan</Button>
              <Button variant="outline" size="sm">
                Change Plan
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Usage This Month</CardTitle>
            <CardDescription>Your current usage across all features</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Team Members</span>
                <span>
                  {billingData.usage.users.current} / {billingData.usage.users.limit}
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${(billingData.usage.users.current / billingData.usage.users.limit) * 100}%` }}
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Storage</span>
                <span>
                  {billingData.usage.storage.current} / {billingData.usage.storage.limit}
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-primary h-2 rounded-full" style={{ width: "24%" }} />
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>API Calls</span>
                <span>
                  {billingData.usage.apiCalls.current.toLocaleString()} /{" "}
                  {billingData.usage.apiCalls.limit.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full"
                  style={{ width: `${(billingData.usage.apiCalls.current / billingData.usage.apiCalls.limit) * 100}%` }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Payment Method</CardTitle>
          <CardDescription>Manage your payment information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded">
                <CreditCard className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-medium">•••• •••• •••• 4242</p>
                <p className="text-sm text-muted-foreground">Expires 12/2025</p>
              </div>
            </div>
            <Badge>Default</Badge>
          </div>

          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Payment Method
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>View your recent invoices</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {billingData.invoices.map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between p-3 border-b last:border-0">
                <div>
                  <p className="font-medium">{invoice.id}</p>
                  <p className="text-sm text-muted-foreground">{invoice.date}</p>
                </div>
                <div className="flex items-center gap-4">
                  <p className="font-medium">{invoice.amount}</p>
                  <Badge
                    variant={
                      invoice.status === "paid" ? "outline" : invoice.status === "pending" ? "secondary" : "destructive"
                    }
                  >
                    {invoice.status}
                  </Badge>
                  <Button variant="ghost" size="sm">
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
