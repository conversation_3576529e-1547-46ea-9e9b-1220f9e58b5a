import { BarChart3, Clock, CreditCard, Globe, Lock, Shield } from "lucide-react"

export function FeaturesGrid() {
  return (
    <section id="features" className="container px-4 md:px-6 py-12 md:py-24 lg:py-32 bg-muted/50">
      <div className="mx-auto flex max-w-[58rem] flex-col items-center justify-center gap-4 text-center">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">Features</h2>
        <p className="max-w-[85%] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
          Our platform offers everything you need to run your South African business efficiently
        </p>
      </div>

      <div className="mx-auto grid max-w-5xl items-center gap-6 py-12 md:grid-cols-2 md:gap-10 lg:grid-cols-3">
        <div className="grid gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Shield className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-lg font-bold">SA Tax Compliance</h3>
          <p className="text-sm text-muted-foreground">
            Built-in South African tax validation and compliance features to keep your business in good standing.
          </p>
        </div>

        <div className="grid gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <BarChart3 className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-lg font-bold">Advanced Analytics</h3>
          <p className="text-sm text-muted-foreground">
            Comprehensive dashboards with real-time metrics to track your business performance.
          </p>
        </div>

        <div className="grid gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <CreditCard className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-lg font-bold">Secure Payments</h3>
          <p className="text-sm text-muted-foreground">
            Integrated payment processing with support for all major South African payment methods.
          </p>
        </div>

        <div className="grid gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Clock className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-lg font-bold">Time-Saving Automation</h3>
          <p className="text-sm text-muted-foreground">
            Automate repetitive tasks and workflows to save time and reduce errors.
          </p>
        </div>

        <div className="grid gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Globe className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-lg font-bold">Multi-Currency Support</h3>
          <p className="text-sm text-muted-foreground">
            Handle transactions in ZAR and other major currencies with automatic exchange rate calculations.
          </p>
        </div>

        <div className="grid gap-2">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10">
            <Lock className="h-5 w-5 text-primary" />
          </div>
          <h3 className="text-lg font-bold">Enterprise Security</h3>
          <p className="text-sm text-muted-foreground">
            Bank-level security protocols to protect your sensitive business data.
          </p>
        </div>
      </div>
    </section>
  )
}
