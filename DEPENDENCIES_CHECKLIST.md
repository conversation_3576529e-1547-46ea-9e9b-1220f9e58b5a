# 📦 FlowIQ Dependencies & Environment Checklist

This checklist ensures you have everything needed to run FlowIQ successfully.

## ✅ System Requirements

### Required Software
- [ ] **Node.js** v18.0.0+ ([Download](https://nodejs.org/))
- [ ] **npm** v8.0.0+ (comes with Node.js)
- [ ] **Python** 3.11+ ([Download](https://python.org/))
- [ ] **PostgreSQL** 13+ ([Download](https://postgresql.org/))
- [ ] **Redis** 6.0+ ([Download](https://redis.io/))
- [ ] **Git** Latest ([Download](https://git-scm.com/))

### Optional but Recommended
- [ ] **Docker Desktop** ([Download](https://docker.com/))
- [ ] **VS Code** with extensions:
  - [ ] TypeScript and JavaScript Language Features
  - [ ] Tailwind CSS IntelliSense
  - [ ] Python
  - [ ] PostgreSQL
- [ ] **Postman** or **Insomnia** for API testing

## 🔧 Installation Commands

### macOS (using Homebrew)
```bash
# Install Homebrew first
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install all dependencies
brew install node python postgresql redis git
brew install --cask docker
brew services start postgresql
brew services start redis
```

### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install other dependencies
sudo apt install python3.11 python3.11-venv python3-pip postgresql postgresql-contrib redis-server git

# Start services
sudo systemctl start postgresql redis-server
sudo systemctl enable postgresql redis-server
```

### Windows
```powershell
# Using Chocolatey (install Chocolatey first)
choco install nodejs python postgresql redis-64 git docker-desktop

# Or use Windows Subsystem for Linux (WSL2) - Recommended
```

## 📋 Environment Setup Checklist

### 1. Database Setup
- [ ] PostgreSQL is running
- [ ] Created database user: `flowiq_user`
- [ ] Created databases: `flowiq_dev`, `flowiq_test`, `flowiq_prod`
- [ ] Set secure password for database user
- [ ] Granted privileges to user

```bash
# Commands to run:
sudo -u postgres createuser --interactive flowiq_user
sudo -u postgres createdb flowiq_dev
sudo -u postgres psql -c "ALTER USER flowiq_user PASSWORD 'your_secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE flowiq_dev TO flowiq_user;"
```

### 2. Redis Setup
- [ ] Redis is running on port 6379
- [ ] Can connect to Redis

```bash
# Test Redis connection:
redis-cli ping
# Should return: PONG
```

### 3. Project Setup
- [ ] Cloned FlowIQ repository
- [ ] Made setup scripts executable
- [ ] Ran quick-start script

```bash
# Commands to run:
git clone <your-flowiq-repo>
cd flowiq-workspace
chmod +x scripts/quick-start.sh
./scripts/quick-start.sh
```

### 4. Environment Variables
- [ ] Created `.env.local` from `.env.example`
- [ ] Created `backend/.env` from `backend/.env.example`
- [ ] Updated all placeholder values
- [ ] Generated secure JWT secrets
- [ ] Configured database credentials

### 5. Frontend Dependencies
- [ ] Root workspace dependencies installed
- [ ] Dashboard dependencies installed
- [ ] Marketing dependencies installed
- [ ] Auth service dependencies installed

```bash
# Verify with:
npm list --depth=0
```

### 6. Backend Dependencies
- [ ] Python virtual environment created
- [ ] Virtual environment activated
- [ ] Python dependencies installed
- [ ] Database migrations applied
- [ ] Django superuser created

```bash
# Verify with:
cd backend && source venv/bin/activate
pip list
python manage.py check
```

## 🚀 Development Servers Checklist

### Frontend Servers
- [ ] Dashboard running on port 3000
- [ ] Marketing running on port 3001
- [ ] Auth service running on port 3002

```bash
# Start commands:
npm run dev:dashboard
npm run dev:marketing
npm run dev:auth-service

# Or all at once:
npm run dev:all
```

### Backend Services
- [ ] Django server running on port 8000
- [ ] Celery worker running
- [ ] Redis accessible

```bash
# Start commands:
cd backend && source venv/bin/activate
python manage.py runserver
celery -A flowiq_backend worker -l info
```

### Verification URLs
- [ ] Dashboard: http://localhost:3000
- [ ] Marketing: http://localhost:3001
- [ ] Auth Service: http://localhost:3002
- [ ] Backend API: http://localhost:8000/api/v1/
- [ ] Django Admin: http://localhost:8000/admin/
- [ ] API Docs: http://localhost:8000/api/docs/

## 🔍 Troubleshooting Checklist

### Common Issues
- [ ] **Port conflicts**: Check if ports 3000, 3001, 3002, 8000 are available
- [ ] **Database connection**: Verify PostgreSQL is running and credentials are correct
- [ ] **Redis connection**: Verify Redis is running on port 6379
- [ ] **Python virtual environment**: Ensure it's activated before running Django commands
- [ ] **Node modules**: Try cleaning and reinstalling if build fails

### Quick Fixes
```bash
# Check port usage
lsof -i :3000
lsof -i :8000

# Clean and reinstall
npm run clean
npm install

# Reset database
dropdb flowiq_dev && createdb flowiq_dev
cd backend && python manage.py migrate

# Restart services
brew services restart postgresql
brew services restart redis
```

## 📦 Package Versions

### Frontend (Node.js)
```json
{
  "node": ">=18.0.0",
  "npm": ">=8.0.0",
  "next": "14.2.16",
  "react": "^18",
  "typescript": "^5",
  "tailwindcss": "^3.4.17"
}
```

### Backend (Python)
```txt
Python>=3.11
Django==4.2.7
djangorestframework==3.14.0
psycopg2-binary==2.9.9
celery==5.3.4
redis==5.0.1
```

## 🔐 Security Checklist

### Development
- [ ] Generated secure JWT secrets
- [ ] Set strong database passwords
- [ ] Environment files not committed to git
- [ ] CORS configured for development URLs

### Production (Future)
- [ ] All secrets changed from defaults
- [ ] HTTPS/SSL certificates configured
- [ ] Database backups enabled
- [ ] Monitoring and logging set up
- [ ] Rate limiting configured

## 🎯 Next Steps After Setup

1. **✅ Verify all services are running**
2. **🔧 Configure external services** (email, storage, etc.)
3. **🧪 Run tests** to ensure everything works
4. **📊 Add real data** to replace mock data
5. **🚀 Deploy** to staging/production

## 📞 Getting Help

If you encounter issues:

1. **Check this checklist** - ensure all items are completed
2. **Review logs** - check terminal output for error messages
3. **Consult documentation** - see SETUP_GUIDE.md for detailed instructions
4. **Check GitHub issues** - search for similar problems
5. **Ask for help** - create a new issue with error details

---

**🎉 Once all items are checked, you're ready to develop FlowIQ!**
