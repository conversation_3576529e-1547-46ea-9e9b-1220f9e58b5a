export function formatCurrency(amount: number, currency = "ZAR"): string {
  return new Intl.NumberFormat("en-ZA", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount)
}

export function formatDate(date: Date | string, format: "short" | "long" | "relative" = "short"): string {
  const dateObj = typeof date === "string" ? new Date(date) : date

  switch (format) {
    case "long":
      return dateObj.toLocaleDateString("en-ZA", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    case "relative":
      return new Intl.RelativeTimeFormat("en-ZA").format(
        Math.floor((dateObj.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
        "day",
      )
    default:
      return dateObj.toLocaleDateString("en-ZA")
  }
}

export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`
}
