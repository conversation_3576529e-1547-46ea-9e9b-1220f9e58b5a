import { z } from "zod"

// South African specific validators
export const saIdNumberSchema = z.string().regex(/^\d{13}$/, "Invalid SA ID number format")

export const saVatNumberSchema = z.string().regex(/^4\d{9}$/, "Invalid SA VAT number format")

export const saPhoneSchema = z.string().regex(/^(\+27|0)[1-9]\d{8}$/, "Invalid SA phone number")

// Common validation schemas
export const emailSchema = z.string().email("Invalid email address")

export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/\d/, "Password must contain at least one number")
  .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character")

export const currencySchema = z.number().positive("Amount must be positive")

export const skuSchema = z.string().min(3, "SKU must be at least 3 characters")
