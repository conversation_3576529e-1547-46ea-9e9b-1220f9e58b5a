{"name": "@flowiq/utils", "version": "0.1.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "type-check": "tsc --noEmit"}, "dependencies": {"@flowiq/types": "*", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "zod": "^3.21.0"}, "devDependencies": {"tsup": "^7.0.0", "typescript": "^5.0.0"}}