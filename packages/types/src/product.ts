export interface Product {
  id: string
  organizationId: string
  name: string
  description?: string
  sku: string
  barcode?: string
  category: string
  price: number
  costPrice?: number
  vatRate: number
  stockLevel: number
  minStockLevel?: number
  unit: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ProductCategory {
  id: string
  name: string
  description?: string
  parentId?: string
  organizationId: string
}

export interface InventoryLocation {
  id: string
  name: string
  address?: string
  organizationId: string
}

export interface StockLevel {
  id: string
  productId: string
  locationId: string
  quantity: number
  reservedQuantity: number
  updatedAt: Date
}
