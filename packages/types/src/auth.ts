export interface User {
  id: string
  email: string
  name: string
  role?: UserRole
  organizationId?: string
  createdAt: Date
  updatedAt: Date
}

export interface AuthSession {
  user: User
  accessToken: string
  refreshToken: string
  expiresAt: Date
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface AuthResponse {
  success: boolean
  user?: User
  session?: AuthSession
  error?: string
}

export type UserRole = "owner" | "admin" | "member" | "viewer"

export type UserStatus = "visitor" | "registered" | "onboarded" | "active"

export type AccessLevel = "discovery" | "registration" | "onboarded"
