export interface Organization {
  id: string
  name: string
  registrationNumber?: string
  vatNumber?: string
  address: Address
  contactInfo: ContactInfo
  settings: OrganizationSettings
  createdAt: Date
  updatedAt: Date
}

export interface Address {
  street: string
  city: string
  province: string
  postalCode: string
  country: string
}

export interface ContactInfo {
  email: string
  phone: string
  website?: string
}

export interface OrganizationSettings {
  currency: string
  timezone: string
  fiscalYearStart: string
  vatRegistered: boolean
  defaultVatRate: number
}
