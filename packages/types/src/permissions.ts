export interface Permission {
  id: string
  name: string
  description: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: Permission[]
}

export interface UserPermissions {
  userId: string
  roles: Role[]
  accessLevel: AccessLevel
  planType: PlanType
  features: FeatureAccess[]
}

export interface FeatureAccess {
  feature: string
  enabled: boolean
  limit?: number
  used?: number
}

export type PlanType = "free" | "starter" | "growth" | "enterprise"

export type AccessLevel = "discovery" | "registration" | "onboarded"
