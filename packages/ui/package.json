{"name": "@flowiq/ui", "version": "0.1.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./styles": "./dist/styles.css"}, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"@flowiq/types": "*", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "tsup": "^7.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}