{"name": "@flowiq/marketing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@flowiq/ui-components": "*", "@flowiq/shared-types": "*", "@flowiq/shared-utils": "*", "next": "14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.45.0", "@hookform/resolvers": "^3.1.0", "zod": "^3.21.0", "next-themes": "^0.2.1", "lucide-react": "^0.263.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24"}}