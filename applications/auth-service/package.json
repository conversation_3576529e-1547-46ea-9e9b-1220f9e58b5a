{"name": "@flowiq/auth-service", "version": "1.0.0", "description": "Secure authentication service for FlowIQ platform", "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@flowiq/shared-types": "*", "@flowiq/shared-utils": "*", "next": "14.0.0", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.2.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "rate-limiter-flexible": "^3.0.8", "helmet": "^7.1.0", "cors": "^2.8.5", "cookie": "^0.5.0"}, "devDependencies": {"@types/node": "20.8.0", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cookie": "^0.5.4", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "eslint": "^8.0.0", "eslint-config-next": "14.0.0"}}