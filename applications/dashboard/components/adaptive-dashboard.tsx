"use client"

import { useEffect, useState } from "react"
import type { BusinessProfile, DashboardConfig, AIRecommendation } from "@flowiq/shared-types"
import { WelcomeMessage } from "./widgets/welcome-message"
import { InventorySnapshot } from "./widgets/inventory-snapshot"
import { SalesPerformance } from "./widgets/sales-performance"
import { ActiveProjects } from "./widgets/active-projects"
import { CashFlowSummary } from "./widgets/cash-flow-summary"
import { AIRecommendations } from "./widgets/ai-recommendations"
import { IndustryInsights } from "./widgets/industry-insights"
import { ComplianceAlerts } from "./widgets/compliance-alerts"
import { ActivityFeed } from "./widgets/activity-feed"
import { QuickActions } from "./widgets/quick-actions"

interface AdaptiveDashboardProps {
  businessProfile: BusinessProfile
  userId: string
}

export function AdaptiveDashboard({ businessProfile, userId }: AdaptiveDashboardProps) {
  const [dashboardConfig, setDashboardConfig] = useState<DashboardConfig | null>(null)
  const [aiRecommendations, setAIRecommendations] = useState<AIRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadDashboardData() {
      try {
        // Load personalized dashboard configuration
        const configResponse = await fetch(`/api/dashboard/config?businessId=${businessProfile.id}`)
        const config = await configResponse.json()

        // Load AI recommendations
        const recommendationsResponse = await fetch(`/api/ai/recommendations?businessId=${businessProfile.id}`)
        const recommendations = await recommendationsResponse.json()

        setDashboardConfig(config)
        setAIRecommendations(recommendations)
      } catch (error) {
        console.error("Failed to load dashboard data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadDashboardData()
  }, [businessProfile.id])

  if (isLoading || !dashboardConfig) {
    return <DashboardSkeleton />
  }

  const getWelcomeMessage = () => {
    const timeOfDay = new Date().getHours() < 12 ? "morning" : new Date().getHours() < 17 ? "afternoon" : "evening"

    const businessTypeMessage = {
      "product-based": "get your products flying off the shelves",
      "service-based": "get your projects billed efficiently",
      hybrid: "grow your business across all fronts",
    }

    return `Good ${timeOfDay}, ${businessProfile.organizationId}! Let's ${businessTypeMessage[businessProfile.businessType]} today.`
  }

  const renderWidget = (widget: any) => {
    switch (widget.type) {
      case "welcome-message":
        return <WelcomeMessage key={widget.id} message={getWelcomeMessage()} />

      case "inventory-snapshot":
        return businessProfile.businessType !== "service-based" ? (
          <InventorySnapshot key={widget.id} businessProfile={businessProfile} aiEnhanced={widget.aiEnhanced} />
        ) : null

      case "sales-performance":
        return businessProfile.businessType !== "service-based" ? (
          <SalesPerformance key={widget.id} businessProfile={businessProfile} />
        ) : null

      case "active-projects":
        return businessProfile.businessType !== "product-based" ? (
          <ActiveProjects key={widget.id} businessProfile={businessProfile} aiEnhanced={widget.aiEnhanced} />
        ) : null

      case "cash-flow-summary":
        return <CashFlowSummary key={widget.id} businessProfile={businessProfile} aiEnhanced={widget.aiEnhanced} />

      case "ai-recommendations":
        return (
          <AIRecommendations key={widget.id} recommendations={aiRecommendations} businessProfile={businessProfile} />
        )

      case "industry-insights":
        return (
          <IndustryInsights
            key={widget.id}
            industry={businessProfile.industry}
            businessType={businessProfile.businessType}
          />
        )

      case "compliance-alerts":
        return <ComplianceAlerts key={widget.id} businessProfile={businessProfile} />

      case "activity-feed":
        return (
          <ActivityFeed
            key={widget.id}
            businessId={businessProfile.id}
            limit={dashboardConfig.preferences.activityFeedLimit}
          />
        )

      case "quick-actions":
        return <QuickActions key={widget.id} businessProfile={businessProfile} />

      default:
        return null
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {dashboardConfig.widgets
          .filter((widget) => widget.visible)
          .sort(
            (a, b) =>
              dashboardConfig.layout.widgetOrder.indexOf(a.id) - dashboardConfig.layout.widgetOrder.indexOf(b.id),
          )
          .map(renderWidget)
          .filter(Boolean)}
      </div>
    </div>
  )
}

function DashboardSkeleton() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="lg:col-span-4 h-48 bg-muted animate-pulse rounded-lg" />
        ))}
      </div>
    </div>
  )
}
