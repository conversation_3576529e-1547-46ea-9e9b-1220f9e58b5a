"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@flowiq/ui-components"
import { Badge } from "@flowiq/ui-components"
import { Button } from "@flowiq/ui-components"
import { Package, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>an, TrendingUp } from "lucide-react"
import type { BusinessProfile } from "@flowiq/shared-types"
import { formatCurrency } from "@flowiq/shared-utils"

interface InventorySnapshotProps {
  businessProfile: BusinessProfile
  aiEnhanced: boolean
}

interface InventoryData {
  totalProducts: number
  totalValue: number
  lowStockItems: number
  outOfStockItems: number
  lastScanTime?: Date
  itemsScannedToday: number
  topSellingProduct?: string
  aiRecommendations: string[]
}

export function InventorySnapshot({ businessProfile, aiEnhanced }: InventorySnapshotProps) {
  const [data, setData] = useState<InventoryData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadInventoryData() {
      try {
        const response = await fetch(`/api/inventory/snapshot?businessId=${businessProfile.id}`)
        const inventoryData = await response.json()
        setData(inventoryData)
      } catch (error) {
        console.error("Failed to load inventory data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadInventoryData()
  }, [businessProfile.id])

  if (isLoading) {
    return <InventorySnapshotSkeleton />
  }

  if (!data) {
    return null
  }

  const hasFlowScan = businessProfile.selectedProducts.includes("flow-scan")
  const urgentAlerts = data.lowStockItems + data.outOfStockItems

  return (
    <Card className="lg:col-span-6">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Package className="h-5 w-5" />
          Inventory Snapshot
          {aiEnhanced && (
            <Badge variant="secondary" className="text-xs">
              AI Enhanced
            </Badge>
          )}
        </CardTitle>
        {urgentAlerts > 0 && (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            {urgentAlerts} Alert{urgentAlerts > 1 ? "s" : ""}
          </Badge>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Total Products</p>
            <p className="text-2xl font-bold">{data.totalProducts}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Total Value</p>
            <p className="text-2xl font-bold">{formatCurrency(data.totalValue)}</p>
          </div>
        </div>

        {hasFlowScan && (
          <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Scan className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Flow Scan Integration</span>
              </div>
              <Badge variant="outline">{data.itemsScannedToday} scanned today</Badge>
            </div>
            {data.lastScanTime && (
              <p className="text-xs text-muted-foreground">Last scan: {new Date(data.lastScanTime).toLocaleString()}</p>
            )}
          </div>
        )}

        {urgentAlerts > 0 && (
          <div className="bg-red-50 dark:bg-red-950 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-800 dark:text-red-200">Stock Alerts</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Low Stock: {data.lowStockItems} items</div>
              <div>Out of Stock: {data.outOfStockItems} items</div>
            </div>
          </div>
        )}

        {aiEnhanced && data.aiRecommendations.length > 0 && (
          <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Flow Scan AI Insights</span>
            </div>
            <ul className="text-xs space-y-1">
              {data.aiRecommendations.slice(0, 2).map((recommendation, index) => (
                <li key={index} className="text-muted-foreground">
                  • {recommendation}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex gap-2">
          <Button size="sm" className="flex-1">
            Manage Products
          </Button>
          {hasFlowScan && (
            <Button size="sm" variant="outline">
              <Scan className="h-4 w-4 mr-1" />
              Scan Items
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function InventorySnapshotSkeleton() {
  return (
    <Card className="lg:col-span-6">
      <CardHeader>
        <div className="h-6 bg-muted animate-pulse rounded" />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="h-16 bg-muted animate-pulse rounded" />
          <div className="h-16 bg-muted animate-pulse rounded" />
        </div>
        <div className="h-20 bg-muted animate-pulse rounded" />
        <div className="flex gap-2">
          <div className="h-8 bg-muted animate-pulse rounded flex-1" />
          <div className="h-8 bg-muted animate-pulse rounded w-24" />
        </div>
      </CardContent>
    </Card>
  )
}
