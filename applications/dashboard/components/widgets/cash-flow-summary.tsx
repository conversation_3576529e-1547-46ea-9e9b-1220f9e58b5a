"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@flowiq/ui-components"
import { Badge } from "@flowiq/ui-components"
import { Button } from "@flowiq/ui-components"
import { TrendingUp, TrendingDown, AlertCircle, Bo<PERSON> } from "lucide-react"
import type { BusinessProfile } from "@flowiq/shared-types"
import { formatCurrency } from "@flowiq/shared-utils"

interface CashFlowSummaryProps {
  businessProfile: BusinessProfile
  aiEnhanced: boolean
}

interface CashFlowData {
  currentBalance: number
  projectedBalance: number
  monthlyInflow: number
  monthlyOutflow: number
  upcomingPayments: {
    amount: number
    dueDate: Date
    description: string
  }[]
  outstandingInvoices: {
    amount: number
    daysOverdue: number
    clientName: string
  }[]
  aiInsights: string[]
  vatSubmissionDue?: Date
  provisionalTaxDue?: Date
}

export function CashFlowSummary({ businessProfile, aiEnhanced }: CashFlowSummaryProps) {
  const [data, setData] = useState<CashFlowData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function loadCashFlowData() {
      try {
        const response = await fetch(`/api/cash-flow/summary?businessId=${businessProfile.id}`)
        const cashFlowData = await response.json()
        setData(cashFlowData)
      } catch (error) {
        console.error("Failed to load cash flow data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadCashFlowData()
  }, [businessProfile.id])

  if (isLoading) {
    return <CashFlowSummarySkeleton />
  }

  if (!data) {
    return null
  }

  const netFlow = data.monthlyInflow - data.monthlyOutflow
  const isPositive = netFlow >= 0
  const hasVATCompliance = businessProfile.compliance.vatRegistered

  return (
    <Card className="lg:col-span-6">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-semibold">Cash Flow Management</CardTitle>
        {aiEnhanced && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Bot className="h-3 w-3" />
            Cash Flow IQ
          </Badge>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Current Balance</p>
            <p className="text-2xl font-bold">{formatCurrency(data.currentBalance)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">7-Day Projection</p>
            <div className="flex items-center gap-2">
              <p className="text-2xl font-bold">{formatCurrency(data.projectedBalance)}</p>
              {isPositive ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-600" />
              )}
            </div>
          </div>
        </div>

        <div className="bg-muted/50 p-3 rounded-lg">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Monthly Flow</span>
            <span className={`text-sm font-medium ${isPositive ? "text-green-600" : "text-red-600"}`}>
              {isPositive ? "+" : ""}
              {formatCurrency(netFlow)}
            </span>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Inflow: </span>
              <span className="font-medium text-green-600">{formatCurrency(data.monthlyInflow)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Outflow: </span>
              <span className="font-medium text-red-600">{formatCurrency(data.monthlyOutflow)}</span>
            </div>
          </div>
        </div>

        {data.outstandingInvoices.length > 0 && (
          <div className="bg-yellow-50 dark:bg-yellow-950 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium">Outstanding Invoices</span>
            </div>
            <div className="space-y-1">
              {data.outstandingInvoices.slice(0, 2).map((invoice, index) => (
                <div key={index} className="flex justify-between text-xs">
                  <span>{invoice.clientName}</span>
                  <span className="font-medium">
                    {formatCurrency(invoice.amount)} ({invoice.daysOverdue}d overdue)
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {hasVATCompliance && (data.vatSubmissionDue || data.provisionalTaxDue) && (
          <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">SARS Compliance</span>
            </div>
            <div className="space-y-1 text-xs">
              {data.vatSubmissionDue && (
                <div>VAT submission due: {new Date(data.vatSubmissionDue).toLocaleDateString()}</div>
              )}
              {data.provisionalTaxDue && (
                <div>Provisional tax due: {new Date(data.provisionalTaxDue).toLocaleDateString()}</div>
              )}
            </div>
          </div>
        )}

        {aiEnhanced && data.aiInsights.length > 0 && (
          <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Bot className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Cash Flow IQ Insights</span>
            </div>
            <ul className="text-xs space-y-1">
              {data.aiInsights.slice(0, 2).map((insight, index) => (
                <li key={index} className="text-muted-foreground">
                  • {insight}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="flex gap-2">
          <Button size="sm" className="flex-1">
            View Cash Flow
          </Button>
          <Button size="sm" variant="outline">
            Send Reminders
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

function CashFlowSummarySkeleton() {
  return (
    <Card className="lg:col-span-6">
      <CardHeader>
        <div className="h-6 bg-muted animate-pulse rounded" />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="h-16 bg-muted animate-pulse rounded" />
          <div className="h-16 bg-muted animate-pulse rounded" />
        </div>
        <div className="h-20 bg-muted animate-pulse rounded" />
        <div className="h-16 bg-muted animate-pulse rounded" />
        <div className="flex gap-2">
          <div className="h-8 bg-muted animate-pulse rounded flex-1" />
          <div className="h-8 bg-muted animate-pulse rounded w-24" />
        </div>
      </CardContent>
    </Card>
  )
}
