import { type NextRequest, NextResponse } from "next/server"
import type { BusinessProfile, DashboardConfig, DashboardWidget } from "@flowiq/shared-types"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const businessId = searchParams.get("businessId")

  if (!businessId) {
    return NextResponse.json({ error: "Business ID required" }, { status: 400 })
  }

  try {
    // TODO: Replace with actual database query
    const businessProfile = await getBusinessProfile(businessId)

    if (!businessProfile) {
      return NextResponse.json({ error: "Business not found" }, { status: 404 })
    }

    const dashboardConfig = generateAdaptiveDashboardConfig(businessProfile)

    return NextResponse.json(dashboardConfig)
  } catch (error) {
    console.error("Failed to load dashboard config:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

async function getBusinessProfile(businessId: string): Promise<BusinessProfile | null> {
  // TODO: Replace with actual database query
  return {
    id: businessId,
    organizationId: "Demo Business",
    businessType: "product-based",
    industry: "retail",
    selectedProducts: ["flow-scan", "cash-flow-management", "product-management"],
    businessSize: "small",
    employeeCount: 5,
    location: {
      province: "Gauteng",
      city: "Johannesburg",
      isRural: false,
    },
    compliance: {
      vatRegistered: true,
      vatNumber: "4123456789",
      cipcRegistered: true,
      registrationNumber: "2020/123456/07",
    },
    preferences: {
      primaryLanguage: "en",
      currency: "ZAR",
      fiscalYearStart: "2024-03-01",
      workingHours: {
        start: "08:00",
        end: "17:00",
        workingDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
      },
    },
    onboardingCompleted: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }
}

function generateAdaptiveDashboardConfig(businessProfile: BusinessProfile): DashboardConfig {
  const widgets: DashboardWidget[] = [
    {
      id: "welcome-message",
      type: "welcome-message",
      title: "Welcome",
      size: "full-width",
      position: { row: 0, col: 0 },
      visible: true,
      config: {},
      aiEnhanced: false,
    },
  ]

  // Add business-type specific widgets
  if (businessProfile.businessType === "product-based" || businessProfile.businessType === "hybrid") {
    widgets.push(
      {
        id: "inventory-snapshot",
        type: "inventory-snapshot",
        title: "Inventory Overview",
        size: "large",
        position: { row: 1, col: 0 },
        visible: true,
        config: {},
        aiEnhanced: businessProfile.selectedProducts.includes("flow-scan"),
      },
      {
        id: "sales-performance",
        type: "sales-performance",
        title: "Sales Performance",
        size: "medium",
        position: { row: 1, col: 6 },
        visible: true,
        config: {},
        aiEnhanced: false,
      },
    )
  }

  if (businessProfile.businessType === "service-based" || businessProfile.businessType === "hybrid") {
    widgets.push({
      id: "active-projects",
      type: "active-projects",
      title: "Active Projects",
      size: "large",
      position: { row: 1, col: 0 },
      visible: true,
      config: {},
      aiEnhanced: businessProfile.selectedProducts.includes("business-projects"),
    })
  }

  // Always add cash flow management
  widgets.push({
    id: "cash-flow-summary",
    type: "cash-flow-summary",
    title: "Cash Flow",
    size: "large",
    position: { row: 2, col: 0 },
    visible: true,
    config: {},
    aiEnhanced: businessProfile.selectedProducts.includes("cash-flow-management"),
  })

  // Add AI and insights widgets
  widgets.push(
    {
      id: "ai-recommendations",
      type: "ai-recommendations",
      title: "AI Recommendations",
      size: "medium",
      position: { row: 3, col: 0 },
      visible: true,
      config: {},
      aiEnhanced: true,
    },
    {
      id: "industry-insights",
      type: "industry-insights",
      title: "Industry Insights",
      size: "medium",
      position: { row: 3, col: 4 },
      visible: true,
      config: {},
      aiEnhanced: false,
    },
    {
      id: "compliance-alerts",
      type: "compliance-alerts",
      title: "Compliance",
      size: "medium",
      position: { row: 3, col: 8 },
      visible: businessProfile.compliance.vatRegistered,
      config: {},
      aiEnhanced: false,
    },
  )

  return {
    businessId: businessProfile.id,
    layout: {
      primaryFocus:
        businessProfile.businessType === "product-based"
          ? "inventory"
          : businessProfile.businessType === "service-based"
            ? "projects"
            : "mixed",
      widgetOrder: widgets.map((w) => w.id),
      columnCount: 12,
      compactMode: false,
    },
    widgets,
    preferences: {
      showWelcomeMessage: true,
      aiRecommendationsEnabled: true,
      industryInsightsEnabled: true,
      complianceAlertsEnabled: businessProfile.compliance.vatRegistered,
      activityFeedLimit: 10,
      refreshInterval: 300000, // 5 minutes
    },
  }
}
