import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@flowiq/ui-components";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "FlowIQ Dashboard - Business Management Platform",
  description:
    "Comprehensive business management dashboard for South African businesses",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
