{"name": "@flowiq/ui-components", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint src/", "type-check": "tsc --noEmit", "build": "tsc"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-tooltip": "^1.0.6", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "next-themes": "^0.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}