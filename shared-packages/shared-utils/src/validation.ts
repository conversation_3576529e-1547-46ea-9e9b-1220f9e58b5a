import { z } from "zod"

// South African ID Number validation
export const saIdNumberSchema = z.string().refine((id) => {
  if (id.length !== 13) return false
  if (!/^\d{13}$/.test(id)) return false

  // <PERSON><PERSON> algorithm check
  const digits = id.split("").map(Number)
  let sum = 0
  for (let i = 0; i < 12; i++) {
    if (i % 2 === 0) {
      sum += digits[i]
    } else {
      const doubled = digits[i] * 2
      sum += doubled > 9 ? doubled - 9 : doubled
    }
  }
  const checkDigit = (10 - (sum % 10)) % 10
  return checkDigit === digits[12]
}, "Invalid South African ID number")

// VAT Number validation
export const vatNumberSchema = z.string().refine((vat) => {
  return /^4\d{9}$/.test(vat)
}, "Invalid VAT number format")

// Company registration number
export const companyRegSchema = z.string().refine((reg) => {
  return /^\d{4}\/\d{6}\/\d{2}$/.test(reg)
}, "Invalid company registration format")

// Password validation
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/\d/, "Password must contain at least one number")
  .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character")

// Email validation
export const emailSchema = z.string().email("Invalid email address")

// Phone number validation (SA format)
export const phoneSchema = z.string().refine((phone) => {
  return /^(\+27|0)[1-9]\d{8}$/.test(phone)
}, "Invalid South African phone number")
