// Currency formatting
export function formatCurrency(amount: number, currency: "ZAR" | "USD" | "EUR" = "ZAR"): string {
  const formatters = {
    ZAR: new Intl.NumberFormat("en-ZA", { style: "currency", currency: "ZAR" }),
    USD: new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }),
    EUR: new Intl.NumberFormat("en-EU", { style: "currency", currency: "EUR" }),
  }

  return formatters[currency].format(amount)
}

// Date formatting
export function formatDate(date: Date | string, format: "short" | "long" | "relative" = "short"): string {
  const d = typeof date === "string" ? new Date(date) : date

  switch (format) {
    case "short":
      return d.toLocaleDateString("en-ZA")
    case "long":
      return d.toLocaleDateString("en-ZA", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    case "relative":
      const now = new Date()
      const diffTime = Math.abs(now.getTime() - d.getTime())
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) return "Today"
      if (diffDays === 1) return "Yesterday"
      if (diffDays < 7) return `${diffDays} days ago`
      if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
      return `${Math.floor(diffDays / 30)} months ago`
    default:
      return d.toLocaleDateString("en-ZA")
  }
}

// Percentage formatting
export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`
}

// Number formatting
export function formatNumber(value: number, decimals = 0): string {
  return new Intl.NumberFormat("en-ZA", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value)
}
