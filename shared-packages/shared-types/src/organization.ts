export interface Organization {
  id: string
  name: string
  registrationNumber?: string
  vatNumber?: string
  address: Address
  contact: ContactInfo
  settings: OrganizationSettings
  subscription: SubscriptionInfo
  createdAt: Date
  updatedAt: Date
}

export interface Address {
  street: string
  city: string
  province: string
  postalCode: string
  country: string
}

export interface ContactInfo {
  email: string
  phone: string
  website?: string
}

export interface OrganizationSettings {
  currency: "ZAR" | "USD" | "EUR"
  language: "en" | "af" | "zu" | "xh"
  timezone: string
  fiscalYearStart: string
  vatRegistered: boolean
}

export interface SubscriptionInfo {
  plan: "free" | "starter" | "growth" | "enterprise"
  status: "active" | "cancelled" | "past_due"
  currentPeriodStart: Date
  currentPeriodEnd: Date
  cancelAtPeriodEnd: boolean
}
