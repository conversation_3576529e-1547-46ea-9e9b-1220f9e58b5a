export type UserLevel = "visitor" | "registered" | "onboarded"
export type UserPlan = "free" | "starter" | "growth" | "enterprise"

export interface UserPermissions {
  level: UserLevel
  plan: UserPlan
  features: FeaturePermissions
  limits: UsageLimits
}

export interface FeaturePermissions {
  dashboard: boolean
  products: boolean
  analytics: boolean
  team: boolean
  integrations: boolean
  api: boolean
  exports: boolean
  customReports: boolean
}

export interface UsageLimits {
  maxProducts: number
  maxUsers: number
  maxTransactions: number
  maxStorageGB: number
  apiCallsPerMonth: number
}

export interface AccessLevel {
  canView: boolean
  canEdit: boolean
  canDelete: boolean
  canInvite: boolean
  canManageBilling: boolean
}
