export interface Product {
  id: string
  organizationId: string
  name: string
  description?: string
  sku: string
  barcode?: string
  category: ProductCategory
  price: number
  costPrice?: number
  vatRate: number
  unit: string
  inventory: InventoryLevel[]
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ProductCategory {
  id: string
  name: string
  description?: string
  parentId?: string
}

export interface InventoryLevel {
  id: string
  productId: string
  locationId: string
  quantity: number
  reservedQuantity: number
  reorderLevel: number
  maxLevel?: number
  lastUpdated: Date
}

export interface BarcodeValidation {
  isValid: boolean
  type?: "EAN13" | "EAN8" | "UPC" | "Code128" | "Code39"
  checkDigit?: string
}
