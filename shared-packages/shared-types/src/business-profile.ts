export type BusinessType = "product-based" | "service-based" | "hybrid"

export type SAIndustry =
  | "retail"
  | "hospitality"
  | "construction"
  | "professional-services"
  | "healthcare"
  | "manufacturing"
  | "agriculture"
  | "automotive"
  | "beauty-wellness"
  | "education"
  | "technology"
  | "creative-agency"
  | "consulting"
  | "trades"
  | "e-commerce"
  | "other"

export type FlowIQProduct =
  | "flow-scan"
  | "cash-flow-management"
  | "business-projects"
  | "product-management"
  | "ai-insights"

export interface BusinessProfile {
  id: string
  organizationId: string
  businessType: BusinessType
  industry: SAIndustry
  subIndustry?: string
  selectedProducts: FlowIQProduct[]
  businessSize: "micro" | "small" | "medium"
  employeeCount: number
  monthlyRevenue?: number
  location: {
    province: string
    city: string
    isRural: boolean
  }
  compliance: {
    vatRegistered: boolean
    vatNumber?: string
    cipcRegistered: boolean
    registrationNumber?: string
    beeLevel?: string
  }
  preferences: {
    primaryLanguage: "en" | "af" | "zu" | "xh" | "st" | "tn" | "ss" | "ve" | "ts" | "nr"
    currency: "ZAR"
    fiscalYearStart: string
    workingHours: {
      start: string
      end: string
      workingDays: string[]
    }
  }
  onboardingCompleted: boolean
  createdAt: Date
  updatedAt: Date
}

export interface IndustryBenchmark {
  industry: SAIndustry
  metrics: {
    averageGrossMargin: number
    averageProjectDuration: number
    averageCustomerAcquisitionCost: number
    averageCustomerLifetimeValue: number
    averageInventoryTurnover?: number
    averagePaymentTerms: number
  }
  trends: {
    growthRate: number
    seasonalPatterns: string[]
    commonChallenges: string[]
  }
  compliance: {
    commonRequirements: string[]
    sarsDeadlines: string[]
    industrySpecificRegulations: string[]
  }
}
