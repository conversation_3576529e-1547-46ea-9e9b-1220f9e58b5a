export interface DashboardConfig {
  businessId: string
  layout: DashboardLayout
  widgets: DashboardWidget[]
  preferences: DashboardPreferences
}

export interface DashboardLayout {
  primaryFocus: "inventory" | "projects" | "cash-flow" | "sales" | "mixed"
  widgetOrder: string[]
  columnCount: number
  compactMode: boolean
}

export interface DashboardWidget {
  id: string
  type: DashboardWidgetType
  title: string
  size: "small" | "medium" | "large" | "full-width"
  position: { row: number; col: number }
  visible: boolean
  config: Record<string, any>
  aiEnhanced: boolean
}

export type DashboardWidgetType =
  | "welcome-message"
  | "inventory-snapshot"
  | "sales-performance"
  | "active-projects"
  | "cash-flow-summary"
  | "ai-recommendations"
  | "industry-insights"
  | "compliance-alerts"
  | "quick-actions"
  | "activity-feed"
  | "flow-scan-status"
  | "upcoming-deadlines"

export interface DashboardPreferences {
  showWelcomeMessage: boolean
  aiRecommendationsEnabled: boolean
  industryInsightsEnabled: boolean
  complianceAlertsEnabled: boolean
  activityFeedLimit: number
  refreshInterval: number
}
