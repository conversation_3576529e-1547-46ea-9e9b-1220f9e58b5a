export interface User {
  id: string
  name: string
  email: string
  role: "admin" | "user" | "viewer"
  organizationId?: string
  preferences: UserPreferences
  profile: UserProfile
  createdAt: Date
  updatedAt: Date
}

export interface UserPreferences {
  language: "en" | "af" | "zu" | "xh"
  currency: "ZAR" | "USD" | "EUR"
  timezone: string
  notifications: NotificationSettings
}

export interface UserProfile {
  firstName: string
  lastName: string
  phone?: string
  avatar?: string
  jobTitle?: string
  department?: string
}

export interface NotificationSettings {
  email: boolean
  sms: boolean
  push: boolean
  marketing: boolean
}
