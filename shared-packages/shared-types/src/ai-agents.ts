export type AIAgentType =
  | "flow-scan-ai"
  | "cash-flow-iq"
  | "project-iq"
  | "inventory-iq"
  | "compliance-iq"
  | "general-flow-iq"

export interface AIAgent {
  id: string
  type: AIAgentType
  name: string
  description: string
  capabilities: string[]
  isActive: boolean
}

export interface AIRecommendation {
  id: string
  agentType: AIAgentType
  businessId: string
  title: string
  description: string
  priority: "low" | "medium" | "high" | "urgent"
  category: "inventory" | "cash-flow" | "projects" | "compliance" | "growth" | "efficiency"
  actionRequired: boolean
  suggestedActions: AIAction[]
  context: Record<string, any>
  createdAt: Date
  expiresAt?: Date
  acknowledged: boolean
}

export interface AIAction {
  id: string
  label: string
  type: "navigate" | "api-call" | "external-link" | "modal"
  payload: Record<string, any>
  primary?: boolean
}

export interface AIInsight {
  id: string
  businessId: string
  type: "trend" | "benchmark" | "alert" | "opportunity" | "compliance"
  title: string
  description: string
  data: Record<string, any>
  industryContext?: string
  saSpecificContext?: string
  confidence: number
  createdAt: Date
}
