export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface AuthUser {
  id: string
  name: string
  email: string
  role: "admin" | "user" | "viewer"
  organizationId?: string
  createdAt: Date
  updatedAt: Date
}

export interface AuthSession {
  user: AuthUser
  accessToken: string
  refreshToken: string
  expiresAt: Date
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordChangeRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}
