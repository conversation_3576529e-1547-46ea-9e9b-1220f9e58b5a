#!/bin/bash

# Django management commands for setting up the database

# 1. Create and run migrations
python manage.py makemigrations
python manage.py migrate

# 2. Create superuser
python manage.py createsuperuser

# 3. Load initial data (subscription plans, etc.)
python manage.py loaddata initial_subscription_plans.json

# 4. Create database indexes for performance
python manage.py dbshell << EOF
-- Additional performance indexes
CREATE INDEX CONCURRENTLY idx_products_barcode_org ON products(organization_id, barcode);
CREATE INDEX CONCURRENTLY idx_transactions_date_org ON transactions(organization_id, transaction_date DESC);
CREATE INDEX CONCURRENTLY idx_inventory_location_qty ON inventory(location_id, quantity_available);
CREATE INDEX CONCURRENTLY idx_transaction_items_product ON transaction_items(product_id, transaction_id);

-- Partial indexes for active records
CREATE INDEX CONCURRENTLY idx_products_active ON products(organization_id) WHERE is_deleted = false AND is_active = true;
CREATE INDEX CONCURRENTLY idx_customers_active ON customers(organization_id) WHERE is_deleted = false AND is_active = true;

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY idx_transactions_status_date ON transactions(organization_id, status, transaction_date DESC);
CREATE INDEX CONCURRENTLY idx_inventory_product_location ON inventory(product_id, location_id) WHERE quantity_available > 0;
EOF

echo "Database setup complete!"
