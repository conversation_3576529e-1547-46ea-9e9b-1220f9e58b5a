from django.db import models
from decimal import Decimal
from .base import BaseModel
from .organizations import Organization, Location

class Category(BaseModel):
    """Product categories"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    
    # Tax settings
    vat_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('15.00'))  # SA VAT rate
    
    class Meta:
        db_table = 'categories'
        unique_together = ['organization', 'name']
        verbose_name_plural = 'Categories'

class Product(BaseModel):
    """Products with barcode support"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='products')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Basic product info
    name = models.Char<PERSON><PERSON>(max_length=255)
    description = models.TextField(blank=True)
    sku = models.Char<PERSON>ield(max_length=100)  # Internal SKU
    
    # Barcode information
    barcode = models.CharField(max_length=50, db_index=True)  # EAN-13, UPC, etc.
    barcode_type = models.CharField(
        max_length=20,
        choices=[
            ('EAN13', 'EAN-13'),
            ('EAN8', 'EAN-8'),
            ('UPC', 'UPC'),
            ('CODE128', 'Code 128'),
            ('CODE39', 'Code 39'),
        ],
        default='EAN13'
    )
    
    # Pricing
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    vat_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('15.00'))
    
    # Inventory tracking
    track_inventory = models.BooleanField(default=True)
    low_stock_threshold = models.IntegerField(default=10)
    
    # Product attributes
    weight = models.DecimalField(max_digits=8, decimal_places=3, null=True, blank=True)  # kg
    dimensions_length = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)  # cm
    dimensions_width = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)  # cm
    dimensions_height = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)  # cm
    
    # Media
    image = models.ImageField(upload_to='products/', null=True, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'products'
        unique_together = [
            ['organization', 'sku'],
            ['organization', 'barcode'],
        ]
        indexes = [
            models.Index(fields=['organization', 'is_active']),
            models.Index(fields=['barcode']),
            models.Index(fields=['sku']),
            models.Index(fields=['name']),
        ]

class ProductVariant(BaseModel):
    """Product variants (size, color, etc.)"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=255)  # e.g., "Large Red"
    sku = models.CharField(max_length=100)
    barcode = models.CharField(max_length=50, db_index=True)
    
    # Variant-specific pricing
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    selling_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    
    # Variant attributes
    attributes = models.JSONField(default=dict)  # {"size": "Large", "color": "Red"}
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'product_variants'
        unique_together = [
            ['product', 'sku'],
            ['product', 'barcode'],
        ]

class Inventory(BaseModel):
    """Inventory levels per location"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='inventory')
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='inventory')
    
    # Stock levels
    quantity_on_hand = models.IntegerField(default=0)
    quantity_reserved = models.IntegerField(default=0)  # Reserved for orders
    quantity_available = models.IntegerField(default=0)  # on_hand - reserved
    
    # Reorder settings
    reorder_point = models.IntegerField(default=10)
    reorder_quantity = models.IntegerField(default=50)
    
    # Last updated from barcode system
    last_sync_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'inventory'
        unique_together = ['product', 'variant', 'location']
        indexes = [
            models.Index(fields=['location', 'quantity_available']),
            models.Index(fields=['product', 'location']),
        ]
