from django.db import models
from django.core.validators import RegexValidator
from .base import BaseModel
from .users import User

class Organization(BaseModel):
    """Multi-tenant organization model"""
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    
    # Business details
    business_type = models.CharField(
        max_length=50,
        choices=[
            ('sole_proprietor', 'Sole Proprietor'),
            ('partnership', 'Partnership'),
            ('pty_ltd', 'Pty Ltd'),
            ('close_corporation', 'Close Corporation'),
            ('non_profit', 'Non-Profit'),
        ]
    )
    registration_number = models.CharField(max_length=50)
    
    # South African tax details
    vat_registered = models.BooleanField(default=False)
    vat_number = models.CharField(
        max_length=10,
        validators=[RegexValidator(
            regex=r'^4\d{9}$',
            message='VAT number must be 10 digits starting with 4'
        )],
        blank=True
    )
    tax_number = models.Char<PERSON>ield(max_length=20)
    tax_period = models.Char<PERSON>ield(
        max_length=20,
        choices=[
            ('monthly', 'Monthly'),
            ('bimonthly', 'Bi-monthly'),
            ('yearly', 'Yearly'),
        ],
        default='monthly'
    )
    paye_registered = models.BooleanField(default=False)
    paye_number = models.CharField(max_length=20, blank=True)
    
    # Contact information
    email = models.EmailField()
    phone = models.CharField(max_length=15)
    website = models.URLField(blank=True)
    
    # Address
    street_address = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=10)
    province = models.CharField(
        max_length=50,
        choices=[
            ('eastern_cape', 'Eastern Cape'),
            ('free_state', 'Free State'),
            ('gauteng', 'Gauteng'),
            ('kwazulu_natal', 'KwaZulu-Natal'),
            ('limpopo', 'Limpopo'),
            ('mpumalanga', 'Mpumalanga'),
            ('north_west', 'North West'),
            ('northern_cape', 'Northern Cape'),
            ('western_cape', 'Western Cape'),
        ]
    )
    
    # Settings
    logo = models.ImageField(upload_to='org_logos/', null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'organizations'
        indexes = [
            models.Index(fields=['slug']),
            models.Index(fields=['is_active']),
        ]

class OrganizationMember(BaseModel):
    """Organization membership with roles"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='memberships')
    
    role = models.CharField(
        max_length=20,
        choices=[
            ('owner', 'Owner'),
            ('admin', 'Admin'),
            ('member', 'Member'),
            ('viewer', 'Viewer'),
        ]
    )
    
    # Permissions
    can_manage_team = models.BooleanField(default=False)
    can_manage_billing = models.BooleanField(default=False)
    can_view_reports = models.BooleanField(default=True)
    can_export_data = models.BooleanField(default=False)
    can_manage_settings = models.BooleanField(default=False)
    
    joined_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'organization_members'
        unique_together = ['organization', 'user']
        indexes = [
            models.Index(fields=['organization', 'is_active']),
            models.Index(fields=['user', 'is_active']),
        ]

class Location(BaseModel):
    """Physical locations for multi-location businesses"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='locations')
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=20)  # For barcode system integration
    
    # Address
    street_address = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=10)
    province = models.CharField(max_length=50)
    
    # Contact
    phone = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Settings
    is_active = models.BooleanField(default=True)
    is_primary = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'locations'
        unique_together = ['organization', 'code']
        indexes = [
            models.Index(fields=['organization', 'is_active']),
            models.Index(fields=['code']),
        ]
