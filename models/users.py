from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from .base import BaseModel

class User(AbstractUser):
    """Custom user model with South African specific fields"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    phone = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+27[0-9]{9}$',
            message='Phone number must be in format: +27xxxxxxxxx'
        )],
        blank=True
    )
    
    # Profile fields
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    bio = models.TextField(max_length=500, blank=True)
    website = models.URLField(blank=True)
    
    # Preferences
    language = models.CharField(
        max_length=5,
        choices=[
            ('en', 'English'),
            ('af', 'Afrikaans'),
            ('zu', 'Zulu'),
            ('xh', 'Xhosa'),
        ],
        default='en'
    )
    timezone = models.CharField(
        max_length=50,
        default='Africa/Johannesburg'
    )
    currency = models.CharField(
        max_length=3,
        choices=[
            ('ZAR', 'South African Rand'),
            ('USD', 'US Dollar'),
            ('EUR', 'Euro'),
            ('GBP', 'British Pound'),
        ],
        default='ZAR'
    )
    
    # Verification status
    email_verified = models.BooleanField(default=False)
    phone_verified = models.BooleanField(default=False)
    two_factor_enabled = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        db_table = 'users'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['created_at']),
        ]

class UserSession(BaseModel):
    """Track user sessions for security"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    is_active = models.BooleanField(default=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        db_table = 'user_sessions'
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]
