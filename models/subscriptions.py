from django.db import models
from decimal import Decimal
from .base import BaseModel
from .organizations import Organization

class SubscriptionPlan(BaseModel):
    """Available subscription plans"""
    name = models.CharField(max_length=100)
    description = models.TextField()
    
    # Pricing
    price_monthly = models.DecimalField(max_digits=8, decimal_places=2)
    price_annual = models.DecimalField(max_digits=8, decimal_places=2)
    
    # Limits
    max_users = models.IntegerField()
    max_locations = models.IntegerField()
    max_products = models.IntegerField()
    max_transactions_per_month = models.IntegerField()
    storage_limit_gb = models.IntegerField()
    
    # Features
    features = models.JSONField(default=list)  # List of feature names
    
    # API limits
    api_calls_per_minute = models.IntegerField(default=60)
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'subscription_plans'

class Subscription(BaseModel):
    """Organization subscriptions"""
    organization = models.OneToOneField(Organization, on_delete=models.CASCADE, related_name='subscription')
    plan = models.ForeignKey(SubscriptionPlan, on_delete=models.CASCADE)
    
    # Billing
    billing_cycle = models.CharField(
        max_length=20,
        choices=[
            ('monthly', 'Monthly'),
            ('annual', 'Annual'),
        ],
        default='monthly'
    )
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('cancelled', 'Cancelled'),
            ('past_due', 'Past Due'),
            ('trialing', 'Trialing'),
        ],
        default='trialing'
    )
    
    # Dates
    trial_ends_at = models.DateTimeField(null=True, blank=True)
    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()
    cancelled_at = models.DateTimeField(null=True, blank=True)
    
    # Payment
    stripe_subscription_id = models.CharField(max_length=100, blank=True)
    
    class Meta:
        db_table = 'subscriptions'

class Invoice(BaseModel):
    """Billing invoices"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='invoices')
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, related_name='invoices')
    
    # Invoice details
    invoice_number = models.CharField(max_length=50, unique=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    vat_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Dates
    invoice_date = models.DateField()
    due_date = models.DateField()
    paid_at = models.DateTimeField(null=True, blank=True)
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', 'Draft'),
            ('sent', 'Sent'),
            ('paid', 'Paid'),
            ('overdue', 'Overdue'),
            ('cancelled', 'Cancelled'),
        ],
        default='draft'
    )
    
    # Payment
    stripe_invoice_id = models.CharField(max_length=100, blank=True)
    
    class Meta:
        db_table = 'invoices'
        indexes = [
            models.Index(fields=['organization', 'status']),
            models.Index(fields=['due_date', 'status']),
        ]
