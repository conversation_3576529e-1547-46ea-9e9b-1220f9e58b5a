from django.db import models
from decimal import Decimal
from .base import BaseModel
from .organizations import Organization, Location
from .products import Product, ProductVariant
from .users import User

class Customer(BaseModel):
    """Customer information"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='customers')
    
    # Basic info
    name = models.CharField(max_length=255)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=15, blank=True)
    
    # Address
    street_address = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=10, blank=True)
    province = models.CharField(max_length=50, blank=True)
    
    # Business details (for B2B)
    company_name = models.CharField(max_length=255, blank=True)
    vat_number = models.CharField(max_length=15, blank=True)
    tax_number = models.CharField(max_length=20, blank=True)
    
    # Customer type
    customer_type = models.CharField(
        max_length=20,
        choices=[
            ('individual', 'Individual'),
            ('business', 'Business'),
        ],
        default='individual'
    )
    
    # Stats
    total_orders = models.IntegerField(default=0)
    total_spent = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'customers'
        unique_together = ['organization', 'email']
        indexes = [
            models.Index(fields=['organization', 'is_active']),
            models.Index(fields=['email']),
            models.Index(fields=['phone']),
        ]

class Supplier(BaseModel):
    """Supplier information"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='suppliers')
    
    # Basic info
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=255, blank=True)
    email = models.EmailField()
    phone = models.CharField(max_length=15)
    
    # Address
    street_address = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=10)
    province = models.CharField(max_length=50)
    
    # Business details
    vat_number = models.CharField(max_length=15, blank=True)
    tax_number = models.CharField(max_length=20, blank=True)
    
    # Payment terms
    payment_terms = models.CharField(
        max_length=50,
        choices=[
            ('cash', 'Cash'),
            ('30_days', '30 Days'),
            ('60_days', '60 Days'),
            ('90_days', '90 Days'),
        ],
        default='30_days'
    )
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'suppliers'
        unique_together = ['organization', 'name']

class Transaction(BaseModel):
    """Sales and inventory transactions"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='transactions')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='transactions')
    
    # Transaction details
    transaction_number = models.CharField(max_length=50, unique=True)
    transaction_type = models.CharField(
        max_length=20,
        choices=[
            ('sale', 'Sale'),
            ('return', 'Return'),
            ('adjustment', 'Inventory Adjustment'),
            ('transfer', 'Stock Transfer'),
            ('purchase', 'Purchase'),
        ]
    )
    
    # Related entities
    customer = models.ForeignKey(Customer, on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Financial details
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    vat_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    # Payment details
    payment_method = models.CharField(
        max_length=20,
        choices=[
            ('cash', 'Cash'),
            ('card', 'Card'),
            ('eft', 'EFT'),
            ('credit', 'Credit'),
        ],
        default='cash'
    )
    payment_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('paid', 'Paid'),
            ('partial', 'Partially Paid'),
            ('overdue', 'Overdue'),
        ],
        default='pending'
    )
    
    # Barcode system integration
    barcode_system_id = models.CharField(max_length=100, blank=True)  # ID from SQLite system
    synced_at = models.DateTimeField(null=True, blank=True)
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ('draft', 'Draft'),
            ('completed', 'Completed'),
            ('cancelled', 'Cancelled'),
        ],
        default='draft'
    )
    
    # Timestamps
    transaction_date = models.DateTimeField()
    
    class Meta:
        db_table = 'transactions'
        indexes = [
            models.Index(fields=['organization', 'transaction_date']),
            models.Index(fields=['location', 'transaction_date']),
            models.Index(fields=['customer', 'transaction_date']),
            models.Index(fields=['transaction_number']),
            models.Index(fields=['barcode_system_id']),
            models.Index(fields=['status']),
        ]

class TransactionItem(BaseModel):
    """Individual items in a transaction"""
    transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE, null=True, blank=True)
    
    # Item details
    quantity = models.IntegerField()
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    vat_rate = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Calculated amounts
    line_total = models.DecimalField(max_digits=12, decimal_places=2)
    vat_amount = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Barcode scan info
    scanned_barcode = models.CharField(max_length=50, blank=True)
    scan_timestamp = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'transaction_items'
        indexes = [
            models.Index(fields=['transaction']),
            models.Index(fields=['product']),
        ]
