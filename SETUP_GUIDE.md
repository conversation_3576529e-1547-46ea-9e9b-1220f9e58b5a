# 🚀 FlowIQ Complete Setup Guide

This comprehensive guide covers everything needed to kickstart the FlowIQ platform from development to production.

## 📋 Prerequisites

### System Requirements
- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher (or yarn/pnpm)
- **Python**: 3.11 or higher
- **PostgreSQL**: 13 or higher
- **Redis**: 6.0 or higher
- **Git**: Latest version

### Development Tools (Recommended)
- **VS Code** with extensions:
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - Python
  - PostgreSQL
- **Docker Desktop** (for containerized development)
- **Postman** or **Insomnia** (for API testing)

## 🏗️ Architecture Overview

FlowIQ consists of:
- **Frontend**: 3 Next.js applications (Dashboard, Marketing, Auth Service)
- **Backend**: Django REST API with PostgreSQL
- **Shared Packages**: TypeScript types, utilities, and UI components
- **Infrastructure**: Redis for caching, Celery for background tasks

## 📦 Dependencies & Packages

### Frontend Dependencies (Node.js)

#### Root Workspace
```json
{
  "dependencies": {
    "@auth/core": "latest",
    "@flowiq/shared-types": "latest",
    "@flowiq/shared-utils": "latest", 
    "@flowiq/ui-components": "latest",
    "@radix-ui/*": "latest",
    "next": "14.2.16",
    "react": "^18",
    "react-dom": "^18",
    "typescript": "^5",
    "tailwindcss": "^3.4.17",
    "zod": "latest",
    "zustand": "latest"
  }
}
```

#### Dashboard Application
- **Charts**: recharts, date-fns
- **Forms**: react-hook-form, @hookform/resolvers
- **State**: zustand
- **UI**: lucide-react, next-themes

#### Marketing Application  
- **UI**: lucide-react, next-themes
- **Forms**: react-hook-form, zod validation

#### Auth Service
- **Security**: bcryptjs, jsonwebtoken, rate-limiter-flexible
- **Validation**: zod
- **Testing**: jest, @testing-library/react

### Backend Dependencies (Python)

#### Core Django Stack
```txt
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
djangorestframework-simplejwt==5.3.0
psycopg2-binary==2.9.9
```

#### Background Tasks
```txt
celery==5.3.4
django-celery-beat==2.5.0
redis==5.0.1
```

#### Development Tools
```txt
django-debug-toolbar==4.2.0
pytest==7.4.3
black==23.11.0
flake8==6.1.0
```

#### Production
```txt
gunicorn==21.2.0
sentry-sdk==1.38.0
django-ratelimit==4.1.0
```

## 🔧 Environment Setup

### 1. System Installation

#### macOS
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install dependencies
brew install node python postgresql redis git
brew services start postgresql
brew services start redis
```

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python and PostgreSQL
sudo apt install python3.11 python3.11-venv python3-pip postgresql postgresql-contrib redis-server

# Start services
sudo systemctl start postgresql
sudo systemctl start redis-server
sudo systemctl enable postgresql
sudo systemctl enable redis-server
```

#### Windows
```powershell
# Install using Chocolatey
choco install nodejs python postgresql redis-64 git

# Or use Windows Subsystem for Linux (WSL2) - Recommended
```

### 2. Database Setup

#### PostgreSQL Configuration
```bash
# Create database user
sudo -u postgres createuser --interactive flowiq_user

# Create databases
sudo -u postgres createdb flowiq_dev
sudo -u postgres createdb flowiq_test
sudo -u postgres createdb flowiq_prod

# Set password
sudo -u postgres psql
ALTER USER flowiq_user PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE flowiq_dev TO flowiq_user;
GRANT ALL PRIVILEGES ON DATABASE flowiq_test TO flowiq_user;
\q
```

### 3. Project Setup

#### Clone and Initialize
```bash
# Clone the repository
git clone <your-flowiq-repo>
cd flowiq-workspace

# Make setup script executable
chmod +x scripts/setup.sh

# Run automated setup
./scripts/setup.sh
```

#### Manual Setup (Alternative)
```bash
# Install frontend dependencies
npm install

# Install workspace dependencies
npm install --workspace=applications/dashboard
npm install --workspace=applications/marketing  
npm install --workspace=applications/auth-service

# Setup backend
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements/development.txt
```

## 🔐 Environment Variables

### Frontend (.env.local)
```env
# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
NEXTAUTH_SECRET=your-nextauth-secret-key
NEXTAUTH_URL=http://localhost:3000

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_AUTH_SERVICE_URL=http://localhost:3002

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Optional: External Services
NEXT_PUBLIC_MAPBOX_TOKEN=your-mapbox-token
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-ga-id
```

### Backend (.env)
```env
# Django
SECRET_KEY=your-super-secret-django-key-change-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database
DB_NAME=flowiq_dev
DB_USER=flowiq_user
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=5432

# Redis
REDIS_URL=redis://localhost:6379/0

# Email (Development)
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key
DEFAULT_FROM_EMAIL=<EMAIL>

# South African Settings
SA_VAT_RATE=0.15
SA_CURRENCY=ZAR

# Optional: External Services
SENTRY_DSN=your-sentry-dsn
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_STORAGE_BUCKET_NAME=flowiq-storage
```

## 🚀 Development Workflow

### 1. Start Development Servers
```bash
# Terminal 1: Backend
cd backend
source venv/bin/activate
python manage.py runserver

# Terminal 2: Celery Worker
cd backend
source venv/bin/activate
celery -A flowiq_backend worker -l info

# Terminal 3: Dashboard
npm run dev:dashboard

# Terminal 4: Marketing
npm run dev:marketing

# Terminal 5: Auth Service
npm run dev:auth-service
```

### 2. Access Applications
- **Dashboard**: http://localhost:3000
- **Marketing**: http://localhost:3001  
- **Auth Service**: http://localhost:3002
- **Backend API**: http://localhost:8000/api/v1/
- **Django Admin**: http://localhost:8000/admin/
- **API Docs**: http://localhost:8000/api/docs/

### 3. Database Migrations
```bash
cd backend
source venv/bin/activate

# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

## 🐳 Docker Development (Alternative)

### Backend with Docker
```bash
cd backend

# Start services
docker-compose up --build

# Run migrations
docker-compose exec web python manage.py migrate

# Create superuser
docker-compose exec web python manage.py createsuperuser
```

### Full Stack Docker (Coming Soon)
```bash
# Will include frontend applications
docker-compose -f docker-compose.full.yml up --build
```

## 🧪 Testing

### Frontend Testing
```bash
# Run all tests
npm run test

# Test specific application
npm run test --workspace=applications/auth-service

# Type checking
npm run type-check
```

### Backend Testing
```bash
cd backend
source venv/bin/activate

# Run tests
pytest

# With coverage
coverage run -m pytest
coverage report
```

## 📦 Building for Production

### Frontend Build
```bash
# Build all applications
npm run build

# Build specific application
npm run build:dashboard
npm run build:marketing
npm run build:auth-service
```

### Backend Build
```bash
cd backend
source venv/bin/activate

# Collect static files
python manage.py collectstatic --noinput

# Check deployment readiness
python manage.py check --deploy
```

## 🚀 Deployment Options

### Frontend Deployment

#### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy dashboard
cd applications/dashboard
vercel --prod

# Deploy marketing
cd applications/marketing  
vercel --prod
```

#### Netlify
```bash
# Install Netlify CLI
npm i -g netlify-cli

# Deploy
netlify deploy --prod --dir=.next
```

### Backend Deployment

#### Railway (Recommended)
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

#### DigitalOcean App Platform
```bash
# Use the provided Dockerfile
# Configure via DigitalOcean dashboard
```

#### Manual VPS Deployment
```bash
# Install dependencies on server
# Configure Nginx/Apache
# Set up SSL certificates
# Configure environment variables
# Run with Gunicorn + Nginx
```

## 🔧 Production Configuration

### Environment Variables (Production)
- Generate secure random keys for all secrets
- Use managed database services (AWS RDS, DigitalOcean Managed DB)
- Configure Redis Cloud or managed Redis
- Set up email service (SendGrid, AWS SES)
- Configure file storage (AWS S3, DigitalOcean Spaces)
- Set up monitoring (Sentry, LogRocket)

### Security Checklist
- [ ] Change all default passwords and secrets
- [ ] Enable HTTPS/SSL certificates
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable database backups
- [ ] Configure monitoring and logging
- [ ] Set up CI/CD pipelines

## 🆘 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using a port
lsof -i :3000

# Kill process
kill -9 <PID>

# Use different port
npm run dev -- -p 3003
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Reset database
dropdb flowiq_dev
createdb flowiq_dev
python manage.py migrate
```

#### Node Modules Issues
```bash
# Clean install
rm -rf node_modules package-lock.json
npm install

# Clear npm cache
npm cache clean --force
```

#### Python Virtual Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install -r requirements/development.txt
```

## 📞 Support & Resources

### Documentation
- [Next.js Documentation](https://nextjs.org/docs)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)

### FlowIQ Specific
- Check README files in each application directory
- Review TODO comments in the codebase
- API documentation at `/api/docs/` when backend is running

---

**🎉 You're now ready to develop and deploy FlowIQ!**

This setup provides a complete development environment for the FlowIQ platform. Follow the steps in order, and you'll have a fully functional business management platform running locally and ready for production deployment.
