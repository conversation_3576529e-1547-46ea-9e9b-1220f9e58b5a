# Core Django
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1

# Authentication & Authorization
djangorestframework-simplejwt==5.3.0
django-allauth==0.57.0

# Database
psycopg2-binary==2.9.9
django-extensions==3.2.3

# API Documentation
drf-spectacular==0.26.5

# Background Tasks
celery==5.3.4
django-celery-beat==2.5.0
django-celery-results==2.5.1
redis==5.0.1

# File Handling
Pillow==10.1.0
django-storages==1.14.2

# Configuration
python-decouple==3.8

# Utilities
python-slugify==8.0.1
django-model-utils==4.3.1

# South African Specific
# TODO: Add SA-specific packages for ID validation, banking APIs, etc.
