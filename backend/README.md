# FlowIQ Backend API

Production-ready Django REST API for FlowIQ - South African SME Business Management Platform.

## 🚀 Quick Start

### Development Setup

1. **Clone and setup:**
   \`\`\`bash
   cd backend
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   \`\`\`

2. **Start services:**
   \`\`\`bash
   # Start Django server
   python manage.py runserver

   # Start Celery worker (in another terminal)
   celery -A flowiq_backend worker -l info

   # Start Celery beat (for scheduled tasks)
   celery -A flowiq_backend beat -l info
   \`\`\`

3. **Access API:**
   - API: http://localhost:8000/api/v1/
   - Admin: http://localhost:8000/admin/
   - Docs: http://localhost:8000/api/docs/

### Docker Setup

\`\`\`bash
docker-compose up --build
\`\`\`

## 📋 TODO: Replace These Placeholders

### 🔐 Authentication & Security
- [ ] Replace `SECRET_KEY` in production
- [ ] Configure proper email verification tokens
- [ ] Add SA ID number validation algorithm
- [ ] Set up proper password reset tokens
- [ ] Configure OAuth providers (Google, Microsoft)

### 🗄️ Database & Storage
- [ ] Replace development database with production PostgreSQL
- [ ] Configure Redis for production
- [ ] Set up database backups
- [ ] Add database connection pooling
- [ ] Configure file storage (AWS S3, Google Cloud)

### 📧 Email & Communications
- [ ] Replace email service credentials (SendGrid, Mailgun)
- [ ] Create email templates for SA context
- [ ] Add SMS service integration (Clickatell)
- [ ] Configure WhatsApp Business API

### 💰 SA-Specific Integrations
- [ ] Add PayFast payment gateway integration
- [ ] Implement SARS eFiling API connection
- [ ] Add SA banking APIs (Open Banking)
- [ ] Implement proper VAT calculation engine
- [ ] Add CIPC business verification

### 🤖 AI & Analytics
- [ ] Configure OpenAI API for recommendations
- [ ] Add business intelligence algorithms
- [ ] Implement cash flow forecasting
- [ ] Add industry benchmarking data

### 🔧 Production Infrastructure
- [ ] Configure production server (Railway, DigitalOcean)
- [ ] Set up monitoring (Sentry, DataDog)
- [ ] Configure CI/CD pipelines
- [ ] Add proper logging and error tracking
- [ ] Set up SSL certificates

## 🏗️ Architecture

\`\`\`
FlowIQ Backend/
├── apps/
│   ├── authentication/     # User management & auth
│   ├── organizations/      # Business/org management
│   ├── products/          # Product & inventory
│   ├── financial/         # Cash flow & transactions
│   ├── analytics/         # Reporting & insights
│   ├── integrations/      # External APIs
│   └── notifications/     # Email/SMS/Push
├── flowiq_backend/
│   ├── settings/          # Environment configs
│   ├── urls.py           # URL routing
│   └── wsgi.py           # WSGI application
└── requirements/          # Dependencies
\`\`\`

## 🔌 API Endpoints

### Authentication
- `POST /api/v1/auth/register/` - User registration
- `POST /api/v1/auth/login/` - User login
- `POST /api/v1/auth/logout/` - User logout
- `POST /api/v1/auth/refresh/` - Token refresh
- `GET /api/v1/auth/profile/` - User profile

### Organizations
- `GET /api/v1/organizations/` - List organizations
- `POST /api/v1/organizations/` - Create organization
- `GET /api/v1/organizations/{id}/` - Get organization
- `PUT /api/v1/organizations/{id}/` - Update organization

### Products (TODO: Implement)
- `GET /api/v1/products/` - List products
- `POST /api/v1/products/` - Create product
- `GET /api/v1/products/{id}/` - Get product
- `PUT /api/v1/products/{id}/` - Update product

## 🧪 Testing

\`\`\`bash
# Run tests
pytest

# Run with coverage
coverage run -m pytest
coverage report
\`\`\`

## 🚀 Deployment

### Railway Deployment
\`\`\`bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
\`\`\`

### Manual Deployment
1. Set up production server
2. Configure environment variables
3. Run migrations: `python manage.py migrate`
4. Collect static files: `python manage.py collectstatic`
5. Start with Gunicorn: `gunicorn flowiq_backend.wsgi`

## 📞 Support

For development questions or issues:
- Check the TODO comments in the code
- Review the API documentation at `/api/docs/`
- Ensure all environment variables are configured
