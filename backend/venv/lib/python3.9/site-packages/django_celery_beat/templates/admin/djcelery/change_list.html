{% extends "admin/change_list.html" %}
{% load i18n %}

{% block breadcrumbs %}
  <div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label=cl.opts.app_label %}">{{ cl.opts.app_config.verbose_name }}</a>
    &rsaquo; {{ cl.opts.verbose_name_plural|capfirst }}
  </div>
  {% if wrong_scheduler %}
    <ul class="messagelist">
      <li class="warning">
      Periodic tasks won't be dispatched unless you set the
      <code>CELERY_BEAT_SCHEDULER</code> setting to
      <code>djcelery.schedulers.DatabaseScheduler</code>,
      or specify it using the <code>-S</code> option to celerybeat
      </li>
    </ul>
  {% endif %}
{% endblock %}
