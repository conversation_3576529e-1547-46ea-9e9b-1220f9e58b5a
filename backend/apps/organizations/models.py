from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class Organization(models.Model):
    """
    Organization/Business model for SA SMEs.
    TODO: Add SA-specific business fields (CIPC registration, etc.)
    """
    BUSINESS_TYPES = [
        ('product-based', 'Product-Based Business'),
        ('service-based', 'Service-Based Business'),
        ('hybrid', 'Hybrid Business'),
    ]
    
    BUSINESS_SIZES = [
        ('micro', 'Micro (1-5 employees)'),
        ('small', 'Small (6-50 employees)'),
        ('medium', 'Medium (51-200 employees)'),
    ]
    
    SA_INDUSTRIES = [
        ('retail', 'Retail'),
        ('hospitality', 'Hospitality'),
        ('construction', 'Construction'),
        ('professional-services', 'Professional Services'),
        ('healthcare', 'Healthcare'),
        ('manufacturing', 'Manufacturing'),
        ('agriculture', 'Agriculture'),
        ('automotive', 'Automotive'),
        ('beauty-wellness', 'Beauty & Wellness'),
        ('education', 'Education'),
        ('technology', 'Technology'),
        ('creative-agency', 'Creative Agency'),
        ('consulting', 'Consulting'),
        ('trades', 'Trades'),
        ('e-commerce', 'E-commerce'),
        ('other', 'Other'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    
    # Business Classification
    business_type = models.CharField(max_length=20, choices=BUSINESS_TYPES)
    industry = models.CharField(max_length=30, choices=SA_INDUSTRIES)
    sub_industry = models.CharField(max_length=100, blank=True)
    business_size = models.CharField(max_length=10, choices=BUSINESS_SIZES)
    employee_count = models.PositiveIntegerField(default=1)
    
    # Contact Information
    email = models.EmailField()
    phone = models.CharField(max_length=15)
    website = models.URLField(blank=True)
    
    # Address (SA specific)
    address_line_1 = models.CharField(max_length=200)
    address_line_2 = models.CharField(max_length=200, blank=True)
    city = models.CharField(max_length=100)
    province = models.CharField(max_length=50, choices=[
        ('EC', 'Eastern Cape'),
        ('FS', 'Free State'),
        ('GP', 'Gauteng'),
        ('KZN', 'KwaZulu-Natal'),
        ('LP', 'Limpopo'),
        ('MP', 'Mpumalanga'),
        ('NC', 'Northern Cape'),
        ('NW', 'North West'),
        ('WC', 'Western Cape'),
    ])
    postal_code = models.CharField(max_length=10)
    is_rural = models.BooleanField(default=False)
    
    # SA Compliance & Registration
    vat_registered = models.BooleanField(default=False)
    vat_number = models.CharField(max_length=20, blank=True, help_text="SA VAT Number")
    cipc_registered = models.BooleanField(default=False)
    registration_number = models.CharField(max_length=50, blank=True, help_text="CIPC Registration Number")
    bee_level = models.CharField(max_length=10, blank=True, help_text="BEE Level")
    
    # Financial Information
    monthly_revenue = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    fiscal_year_start = models.DateField(help_text="Start of fiscal year")
    
    # FlowIQ Configuration
    selected_products = models.JSONField(default=list, help_text="Selected FlowIQ products")
    
    # Settings
    currency = models.CharField(max_length=3, default='ZAR')
    timezone = models.CharField(max_length=50, default='Africa/Johannesburg')
    primary_language = models.CharField(max_length=2, default='en')
    
    # Status
    is_active = models.BooleanField(default=True)
    onboarding_completed = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'organizations'
        verbose_name = _('Organization')
        verbose_name_plural = _('Organizations')
    
    def __str__(self):
        return self.name
    
    @property
    def full_address(self):
        """Return formatted full address."""
        address_parts = [
            self.address_line_1,
            self.address_line_2,
            self.city,
            self.get_province_display(),
            self.postal_code
        ]
        return ', '.join(filter(None, address_parts))

class OrganizationMember(models.Model):
    """
    Organization membership model.
    TODO: Add role-based permissions for SA business context.
    """
    ROLES = [
        ('owner', 'Business Owner'),
        ('admin', 'Administrator'),
        ('manager', 'Manager'),
        ('accountant', 'Accountant'),
        ('employee', 'Employee'),
        ('viewer', 'Viewer'),
    ]
    
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='members')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='organizations')
    role = models.CharField(max_length=20, choices=ROLES)
    
    # Permissions
    can_manage_products = models.BooleanField(default=False)
    can_manage_finances = models.BooleanField(default=False)
    can_manage_users = models.BooleanField(default=False)
    can_view_reports = models.BooleanField(default=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    invited_at = models.DateTimeField(auto_now_add=True)
    joined_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'organization_members'
        unique_together = ['organization', 'user']
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.organization.name} ({self.role})"
