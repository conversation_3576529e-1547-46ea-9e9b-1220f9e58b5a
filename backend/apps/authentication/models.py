from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

class User(AbstractUser):
    """
    Custom User model for FlowIQ.
    TODO: Customize fields based on your SA SME requirements.
    """
    email = models.EmailField(_('email address'), unique=True)
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    
    # South African specific fields
    id_number = models.CharField(max_length=13, blank=True, null=True, help_text="SA ID Number")
    preferred_language = models.Char<PERSON>ield(
        max_length=2,
        choices=[('en', 'English'), ('af', 'Afrikaans')],
        default='en'
    )
    
    # Profile fields
    profile_picture = models.ImageField(upload_to='profile_pics/', blank=True, null=True)
    is_email_verified = models.BooleanField(default=False)
    is_phone_verified = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(blank=True, null=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        db_table = 'auth_users'
        verbose_name = _('User')
        verbose_name_plural = _('Users')
    
    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

class UserProfile(models.Model):
    """
    Extended user profile for additional SA business context.
    TODO: Add fields specific to SA SME users.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    
    # Business context
    job_title = models.CharField(max_length=100, blank=True)
    company_role = models.CharField(
        max_length=20,
        choices=[
            ('owner', 'Business Owner'),
            ('manager', 'Manager'),
            ('accountant', 'Accountant'),
            ('employee', 'Employee'),
        ],
        default='owner'
    )
    
    # Preferences
    timezone = models.CharField(max_length=50, default='Africa/Johannesburg')
    date_format = models.CharField(max_length=20, default='DD/MM/YYYY')
    currency_display = models.CharField(max_length=10, default='ZAR')
    
    # Onboarding
    onboarding_completed = models.BooleanField(default=False)
    onboarding_step = models.IntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_profiles'
    
    def __str__(self):
        return f"Profile for {self.user.email}"
