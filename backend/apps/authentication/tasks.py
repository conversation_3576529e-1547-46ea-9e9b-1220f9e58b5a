from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from .models import User

@shared_task
def send_welcome_email(user_id):
    """
    Send welcome email to new user.
    TODO: Customize email template for SA SME context.
    """
    try:
        user = User.objects.get(id=user_id)
        
        subject = 'Welcome to FlowIQ - Your SA Business Management Platform'
        
        # TODO: Create proper email template
        html_message = render_to_string('emails/welcome.html', {
            'user': user,
            'company_name': 'FlowIQ',
        })
        
        send_mail(
            subject=subject,
            message='',  # Plain text version
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
        
        return f"Welcome email sent to {user.email}"
    except User.DoesNotExist:
        return f"User with id {user_id} not found"
    except Exception as e:
        return f"Failed to send welcome email: {str(e)}"

@shared_task
def send_verification_email(user_id):
    """
    Send email verification email.
    TODO: Generate verification token and send email.
    """
    try:
        user = User.objects.get(id=user_id)
        
        # TODO: Generate verification token
        verification_token = "generate_verification_token_here"
        
        subject = 'Verify your FlowIQ account'
        
        # TODO: Create proper email template
        html_message = render_to_string('emails/verify_email.html', {
            'user': user,
            'verification_token': verification_token,
            'verification_url': f"{settings.FRONTEND_URL}/verify-email?token={verification_token}",
        })
        
        send_mail(
            subject=subject,
            message='',
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
        
        return f"Verification email sent to {user.email}"
    except User.DoesNotExist:
        return f"User with id {user_id} not found"
    except Exception as e:
        return f"Failed to send verification email: {str(e)}"

@shared_task
def send_password_reset_email(user_id):
    """
    Send password reset email.
    TODO: Generate reset token and send email.
    """
    try:
        user = User.objects.get(id=user_id)
        
        # TODO: Generate password reset token
        reset_token = "generate_reset_token_here"
        
        subject = 'Reset your FlowIQ password'
        
        # TODO: Create proper email template
        html_message = render_to_string('emails/password_reset.html', {
            'user': user,
            'reset_token': reset_token,
            'reset_url': f"{settings.FRONTEND_URL}/reset-password?token={reset_token}",
        })
        
        send_mail(
            subject=subject,
            message='',
            html_message=html_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )
        
        return f"Password reset email sent to {user.email}"
    except User.DoesNotExist:
        return f"User with id {user_id} not found"
    except Exception as e:
        return f"Failed to send password reset email: {str(e)}"
