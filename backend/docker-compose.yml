version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: flowiq_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  web:
    build: .
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DB_HOST=db
      - REDIS_URL=redis://redis:6379/0

  celery:
    build: .
    command: celery -A flowiq_backend worker -l info
    volumes:
      - .:/app
    depends_on:
      - db
      - redis
    environment:
      - DEBUG=1
      - DB_HOST=db
      - REDIS_URL=redis://redis:6379/0

volumes:
  postgres_data:
