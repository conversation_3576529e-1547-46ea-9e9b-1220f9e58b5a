# FlowIQ Backend Environment Variables
# Copy this file to .env and update with your actual values

# =============================================================================
# DJANGO CORE SETTINGS
# =============================================================================

# Django Secret Key (CHANGE IN PRODUCTION!)
SECRET_KEY=your-super-secret-django-key-change-in-production-make-it-very-long-and-random

# Debug mode (False in production)
DEBUG=True

# Allowed hosts (comma-separated)
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Environment
ENVIRONMENT=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database
DB_NAME=flowiq_dev
DB_USER=flowiq_user
DB_PASSWORD=your_secure_database_password
DB_HOST=localhost
DB_PORT=5432

# Database URL (alternative format)
# DATABASE_URL=postgresql://flowiq_user:password@localhost:5432/flowiq_dev

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis for caching and Celery
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Email backend
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend

# SMTP Settings (using SendGrid as example)
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key

# Default email addresses
DEFAULT_FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery broker (usually same as Redis)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# CORS settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
CORS_ALLOW_CREDENTIALS=True

# CSRF settings
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# =============================================================================
# SOUTH AFRICAN SPECIFIC SETTINGS
# =============================================================================

# VAT Rate (15% in South Africa)
SA_VAT_RATE=0.15

# Currency
SA_CURRENCY=ZAR

# Timezone
TIME_ZONE=Africa/Johannesburg

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Sentry for error tracking
SENTRY_DSN=your-sentry-dsn

# AWS S3 for file storage (optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=flowiq-storage
AWS_S3_REGION_NAME=us-east-1

# =============================================================================
# PAYMENT INTEGRATION (FUTURE)
# =============================================================================

# PayFast (South African payment gateway)
PAYFAST_MERCHANT_ID=your-payfast-merchant-id
PAYFAST_MERCHANT_KEY=your-payfast-merchant-key
PAYFAST_PASSPHRASE=your-payfast-passphrase
PAYFAST_SANDBOX=True

# =============================================================================
# SOUTH AFRICAN BANKING APIs (FUTURE)
# =============================================================================

# Open Banking APIs
# NEDBANK_API_KEY=your-nedbank-api-key
# FNB_API_KEY=your-fnb-api-key
# ABSA_API_KEY=your-absa-api-key

# =============================================================================
# GOVERNMENT INTEGRATION (FUTURE)
# =============================================================================

# SARS (South African Revenue Service) API
# SARS_API_KEY=your-sars-api-key
# SARS_ENVIRONMENT=sandbox

# CIPC (Companies and Intellectual Property Commission)
# CIPC_API_KEY=your-cipc-api-key

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log level
LOG_LEVEL=INFO

# Sentry environment
SENTRY_ENVIRONMENT=development

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
ENABLE_API_THROTTLING=True
ENABLE_EMAIL_VERIFICATION=True
ENABLE_SMS_NOTIFICATIONS=False
ENABLE_BARCODE_INTEGRATION=True

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Django Debug Toolbar
ENABLE_DEBUG_TOOLBAR=True

# Django Extensions
ENABLE_DJANGO_EXTENSIONS=True

# =============================================================================
# PRODUCTION SETTINGS (UNCOMMENT FOR PRODUCTION)
# =============================================================================

# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True
# SECURE_CONTENT_TYPE_NOSNIFF=True
# SECURE_BROWSER_XSS_FILTER=True
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env
# 2. Update all "your-*" placeholders with actual values
# 3. Generate a secure Django secret key
# 4. Set up your PostgreSQL database
# 5. Configure your email service
# 6. Never commit .env to version control
# 7. Use different values for development, staging, and production

# Generate Django secret key:
# python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
