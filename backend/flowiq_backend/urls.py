"""
FlowIQ Backend URL Configuration
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),
    
    # API Documentation
    path('api/schema/', SpectacularAPIView.as_view(), name='schema'),
    path('api/docs/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    
    # API v1
    path('api/v1/', include([
        # TODO: FLOWIQ PRODUCTION - Add app URLs when implemented
        # path('auth/', include('apps.authentication.urls')),
        # path('organizations/', include('apps.organizations.urls')),
        # path('products/', include('apps.products.urls')),
        # path('financial/', include('apps.financial.urls')),
        # path('analytics/', include('apps.analytics.urls')),
        # path('integrations/', include('apps.integrations.urls')),
        # path('notifications/', include('apps.notifications.urls')),
    ])),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
