#!/bin/bash

# FlowIQ Backend Setup Script
# TODO: Customize this script for your deployment environment

echo "🚀 Setting up FlowIQ Backend..."

# Create virtual environment
echo "📦 Creating virtual environment..."
python -m venv venv
source venv/bin/activate

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements/development.txt

# Set up environment variables
echo "🔧 Setting up environment variables..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "⚠️  Please update .env file with your configuration"
fi

# Database setup
echo "🗄️  Setting up database..."
python manage.py makemigrations
python manage.py migrate

# Create superuser
echo "👤 Creating superuser..."
echo "Please create a superuser account:"
python manage.py createsuperuser

# Collect static files
echo "📁 Collecting static files..."
python manage.py collectstatic --noinput

echo "✅ Setup complete!"
echo ""
echo "🔥 To start the development server:"
echo "   python manage.py runserver"
echo ""
echo "🔥 To start Celery worker:"
echo "   celery -A flowiq_backend worker -l info"
echo ""
echo "📚 API Documentation available at:"
echo "   http://localhost:8000/api/docs/"
