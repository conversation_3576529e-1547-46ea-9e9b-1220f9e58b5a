# FlowIQ - Complete Modular Workspace

A secure, modular business management platform built with clear separation of concerns.

## 🏗️ Complete Architecture

This is the **COMPLETE WORKSPACE** with all applications and shared packages properly organized.

### 📁 Complete Project Structure

\`\`\`
flowiq-workspace/
├── applications/                    # All deployable applications
│   ├── dashboard/                  # Main business dashboard (port 3000)
│   │   ├── app/                   # Next.js app directory
│   │   ├── package.json           # Dashboard dependencies
│   │   ├── next.config.mjs        # Next.js configuration
│   │   ├── tailwind.config.ts     # Tailwind configuration
│   │   └── globals.css            # Global styles
│   ├── marketing/                  # Public marketing website (port 3001)  
│   │   ├── app/                   # Next.js app directory
│   │   ├── package.json           # Marketing dependencies
│   │   ├── next.config.mjs        # Next.js configuration
│   │   ├── tailwind.config.ts     # Tailwind configuration
│   │   └── globals.css            # Global styles
│   └── auth-service/              # Authentication service (port 3002)
│       ├── app/api/auth/          # Auth API routes
│       ├── lib/                   # Auth utilities
│       └── package.json           # Auth service dependencies
├── shared-packages/               # Reusable packages across applications
│   ├── ui-components/             # Shared UI component library
│   │   ├── src/                   # Component source code
│   │   └── package.json           # UI components dependencies
│   ├── shared-types/              # TypeScript type definitions
│   │   ├── src/                   # Type definitions
│   │   └── package.json           # Types package config
│   └── shared-utils/              # Shared utility functions
│       ├── src/                   # Utility functions
│       └── package.json           # Utils package config
├── scripts/
│   └── setup.sh                  # Automated setup script
├── package.json                   # Workspace configuration
├── .env.example                   # Environment variables template
└── README.md                      # This file
\`\`\`

## 🚀 Quick Start

### 1. Download & Setup
\`\`\`bash
# Extract the downloaded ZIP file
unzip flowiq-workspace.zip
cd flowiq-workspace

# Run the setup script
chmod +x scripts/setup.sh
./scripts/setup.sh
\`\`\`

### 2. Configure Environment
\`\`\`bash
# Update environment variables
cp .env.example .env.local
# Edit .env.local with your actual values
\`\`\`

### 3. Start Development
\`\`\`bash
# Start all services (in separate terminals)
npm run dev:dashboard      # Dashboard (localhost:3000)
npm run dev:marketing      # Marketing (localhost:3001)  
npm run dev:auth-service   # Auth Service (localhost:3002)

# Or start individually
npm run dev:dashboard      # Business dashboard
npm run dev:marketing      # Marketing website
npm run dev:auth-service   # Authentication service
\`\`\`

## 📦 Applications

### Dashboard (`applications/dashboard`)
**Port: 3000** | **Package: @flowiq/dashboard**
- Business management interface
- Product management
- Analytics and reporting
- Team management
- Settings and configuration

### Marketing (`applications/marketing`)
**Port: 3001** | **Package: @flowiq/marketing**
- Public marketing website
- Pricing information
- Contact forms
- ROI calculator
- Lead generation

### Auth Service (`applications/auth-service`)
**Port: 3002** | **Package: @flowiq/auth-service**
- User authentication
- Session management
- Password reset
- Account verification
- Security middleware

## 📚 Shared Packages

### UI Components (`@flowiq/ui-components`)
Shared UI component library with:
- Radix UI primitives
- Tailwind CSS styling
- TypeScript definitions
- Theme support

### Shared Types (`@flowiq/shared-types`)
Centralized TypeScript definitions for:
- User and authentication types
- Product management types
- API response types
- Business logic types

### Shared Utils (`@flowiq/shared-utils`)
Shared utility functions for:
- Form validation (SA-specific)
- Currency/date formatting
- Constants and configuration
- Common helpers

## 🔧 Development Commands

\`\`\`bash
# Development
npm run dev:dashboard      # Start dashboard (port 3000)
npm run dev:marketing      # Start marketing (port 3001)
npm run dev:auth-service   # Start auth service (port 3002)

# Building
npm run build              # Build all applications
npm run build:dashboard    # Build dashboard only
npm run build:marketing    # Build marketing only
npm run build:auth-service # Build auth service only

# Utilities
npm run lint               # Lint all applications
npm run type-check         # Type check all applications
npm run clean              # Clean all node_modules
npm run setup              # Run setup script
\`\`\`

## 🛡️ Security Features

- **Password Security**: Bcrypt hashing with salt rounds
- **Rate Limiting**: Prevents brute force attacks
- **Account Lockout**: Automatic protection after failed attempts
- **JWT Security**: Separate access and refresh tokens
- **Secure Cookies**: HttpOnly, Secure, SameSite protection
- **CORS Protection**: Configurable allowed origins
- **Security Headers**: XSS, CSRF, clickjacking protection

## 🌍 South African Features

- **VAT Compliance**: Built-in VAT rate handling
- **ID Number Validation**: South African ID validation
- **Company Registration**: CIPC number validation
- **Currency Support**: ZAR formatting and calculations
- **Province Support**: All 9 SA provinces
- **Language Support**: English, Afrikaans, Zulu, Xhosa

## 🚀 Deployment

Each application can be deployed independently:

### Dashboard & Marketing
- **Vercel**: \`vercel --prod\`
- **Netlify**: Connect to Git repository
- **Railway**: \`railway up\`

### Auth Service
- **Railway**: Recommended for database access
- **Render**: Good for API services
- **DigitalOcean**: App Platform

## 📝 Next Steps

1. **✅ Complete Workspace Setup** - This is done!
2. **🔧 Configure Environment Variables** - Update .env.local files
3. **🗄️ Add Database Integration** - Connect to PostgreSQL/Supabase
4. **🧹 Strip Mock Data** - Remove placeholder data from APIs
5. **🔗 Connect Services** - Integrate auth service with applications
6. **🚀 Deploy** - Deploy each application independently

## 🆘 Troubleshooting

### Port Conflicts
If ports are in use, update the port numbers in package.json scripts:
\`\`\`json
"dev": "next dev -p 3003"  // Change port number
\`\`\`

### Workspace Issues
\`\`\`bash
# Clean and reinstall
npm run clean
npm install
\`\`\`

### Build Errors
\`\`\`bash
# Type check first
npm run type-check
# Then build
npm run build
\`\`\`

## 📞 Support

- **Documentation**: Check README files in each application
- **Issues**: Create GitHub issues for bugs
- **Questions**: Use GitHub discussions

---

**This is the COMPLETE, READY-TO-DOWNLOAD workspace!** 🎉

Download this version to get the entire modular FlowIQ platform with all applications and shared packages properly organized.
