import { PasswordUtils } from "@/lib/utils/password"
import { JWTUtils } from "@/lib/utils/jwt"
import { SECURITY_CONFIG } from "@/lib/config/security"
import type { User, AuthSession, LoginRequest, RegisterRequest, AuthResponse } from "@/lib/types/auth"

// Mock database - replace with real database in production
const users: Map<string, User & { passwordHash: string }> = new Map()
const sessions: Map<string, AuthSession> = new Map()

export class AuthService {
  static async register(request: RegisterRequest): Promise<AuthResponse> {
    try {
      // Validate input
      if (request.password !== request.confirmPassword) {
        return { success: false, message: "Passwords do not match" }
      }

      const passwordValidation = PasswordUtils.validate(request.password)
      if (!passwordValidation.valid) {
        return { success: false, message: passwordValidation.errors.join(", ") }
      }

      // Check if user already exists
      const existingUser = Array.from(users.values()).find((u) => u.email === request.email)
      if (existingUser) {
        return { success: false, message: "User already exists with this email" }
      }

      // Create new user
      const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const passwordHash = await PasswordUtils.hash(request.password)
      const emailVerificationToken = PasswordUtils.generateSecureToken()

      const newUser: User & { passwordHash: string } = {
        id: userId,
        email: request.email,
        name: request.name,
        passwordHash,
        emailVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        loginAttempts: 0,
        emailVerificationToken,
      }

      users.set(userId, newUser)

      // TODO: Send email verification email
      console.log(`Email verification token for ${request.email}: ${emailVerificationToken}`)

      const { passwordHash: _, ...userWithoutPassword } = newUser
      return {
        success: true,
        message: "Registration successful. Please check your email to verify your account.",
        user: userWithoutPassword,
      }
    } catch (error) {
      console.error("Registration error:", error)
      return { success: false, message: "Registration failed. Please try again." }
    }
  }

  static async login(request: LoginRequest, ipAddress?: string, userAgent?: string): Promise<AuthResponse> {
    try {
      // Find user
      const user = Array.from(users.values()).find((u) => u.email === request.email)
      if (!user) {
        return { success: false, message: "Invalid email or password" }
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        const remainingTime = Math.ceil((user.lockedUntil.getTime() - Date.now()) / 60000)
        return {
          success: false,
          message: `Account is locked. Try again in ${remainingTime} minutes.`,
        }
      }

      // Verify password
      const isValidPassword = await PasswordUtils.verify(request.password, user.passwordHash)
      if (!isValidPassword) {
        // Increment login attempts
        user.loginAttempts += 1
        if (user.loginAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
          user.lockedUntil = new Date(Date.now() + SECURITY_CONFIG.LOCKOUT_DURATION)
        }
        user.updatedAt = new Date()
        users.set(user.id, user)

        return { success: false, message: "Invalid email or password" }
      }

      // Reset login attempts on successful login
      user.loginAttempts = 0
      user.lockedUntil = undefined
      user.lastLoginAt = new Date()
      user.updatedAt = new Date()
      users.set(user.id, user)

      // Create session
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const sessionDuration = request.rememberMe
        ? SECURITY_CONFIG.REMEMBER_ME_DURATION
        : SECURITY_CONFIG.SESSION_DURATION

      const session: AuthSession = {
        userId: user.id,
        email: user.email,
        name: user.name,
        sessionId,
        expiresAt: new Date(Date.now() + sessionDuration),
        ipAddress,
        userAgent,
      }

      sessions.set(sessionId, session)

      // Generate tokens
      const accessToken = JWTUtils.generateAccessToken(user, sessionId)
      const refreshToken = JWTUtils.generateRefreshToken(user, sessionId)

      const { passwordHash: _, ...userWithoutPassword } = user
      return {
        success: true,
        message: "Login successful",
        user: userWithoutPassword,
        token: accessToken,
        refreshToken,
      }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, message: "Login failed. Please try again." }
    }
  }

  static async validateSession(token: string): Promise<{ valid: boolean; user?: User; session?: AuthSession }> {
    try {
      const payload = JWTUtils.verifyAccessToken(token)
      if (!payload) {
        return { valid: false }
      }

      const session = sessions.get(payload.sessionId)
      if (!session || session.expiresAt < new Date()) {
        // Clean up expired session
        if (session) {
          sessions.delete(payload.sessionId)
        }
        return { valid: false }
      }

      const user = users.get(payload.userId)
      if (!user) {
        return { valid: false }
      }

      const { passwordHash: _, ...userWithoutPassword } = user
      return {
        valid: true,
        user: userWithoutPassword,
        session,
      }
    } catch (error) {
      console.error("Session validation error:", error)
      return { valid: false }
    }
  }

  static async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      const payload = JWTUtils.verifyRefreshToken(refreshToken)
      if (!payload) {
        return { success: false, message: "Invalid refresh token" }
      }

      const session = sessions.get(payload.sessionId)
      if (!session || session.expiresAt < new Date()) {
        return { success: false, message: "Session expired" }
      }

      const user = users.get(payload.userId)
      if (!user) {
        return { success: false, message: "User not found" }
      }

      // Generate new tokens
      const newAccessToken = JWTUtils.generateAccessToken(user, payload.sessionId)
      const newRefreshToken = JWTUtils.generateRefreshToken(user, payload.sessionId)

      // Extend session
      session.expiresAt = new Date(Date.now() + SECURITY_CONFIG.SESSION_DURATION)
      sessions.set(payload.sessionId, session)

      const { passwordHash: _, ...userWithoutPassword } = user
      return {
        success: true,
        message: "Token refreshed successfully",
        user: userWithoutPassword,
        token: newAccessToken,
        refreshToken: newRefreshToken,
      }
    } catch (error) {
      console.error("Token refresh error:", error)
      return { success: false, message: "Token refresh failed" }
    }
  }

  static async logout(sessionId: string): Promise<{ success: boolean; message: string }> {
    try {
      sessions.delete(sessionId)
      return { success: true, message: "Logout successful" }
    } catch (error) {
      console.error("Logout error:", error)
      return { success: false, message: "Logout failed" }
    }
  }

  static async requestPasswordReset(email: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = Array.from(users.values()).find((u) => u.email === email)
      if (!user) {
        // Don't reveal if email exists or not
        return { success: true, message: "If an account with that email exists, a password reset link has been sent." }
      }

      const resetToken = PasswordUtils.generateSecureToken()
      user.passwordResetToken = resetToken
      user.passwordResetExpires = new Date(Date.now() + SECURITY_CONFIG.PASSWORD_RESET_EXPIRES)
      user.updatedAt = new Date()
      users.set(user.id, user)

      // TODO: Send password reset email
      console.log(`Password reset token for ${email}: ${resetToken}`)

      return { success: true, message: "If an account with that email exists, a password reset link has been sent." }
    } catch (error) {
      console.error("Password reset request error:", error)
      return { success: false, message: "Password reset request failed. Please try again." }
    }
  }

  static async resetPassword(token: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    try {
      const passwordValidation = PasswordUtils.validate(newPassword)
      if (!passwordValidation.valid) {
        return { success: false, message: passwordValidation.errors.join(", ") }
      }

      const user = Array.from(users.values()).find(
        (u) => u.passwordResetToken === token && u.passwordResetExpires && u.passwordResetExpires > new Date(),
      )

      if (!user) {
        return { success: false, message: "Invalid or expired reset token" }
      }

      // Update password
      user.passwordHash = await PasswordUtils.hash(newPassword)
      user.passwordResetToken = undefined
      user.passwordResetExpires = undefined
      user.updatedAt = new Date()

      // Invalidate all sessions for this user
      for (const [sessionId, session] of sessions.entries()) {
        if (session.userId === user.id) {
          sessions.delete(sessionId)
        }
      }

      users.set(user.id, user)

      return { success: true, message: "Password reset successful. Please log in with your new password." }
    } catch (error) {
      console.error("Password reset error:", error)
      return { success: false, message: "Password reset failed. Please try again." }
    }
  }
}
