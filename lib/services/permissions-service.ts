import type { UserPermissions, FeatureAccess, UserLevel, PlanType } from "@/lib/types/permissions"

export class PermissionsService {
  static getDefaultPermissions(level: UserLevel = "visitor", plan: PlanType = "free"): UserPermissions {
    const basePermissions: UserPermissions = {
      level,
      plan,
      isEmailVerified: false,
      onboardingStep: 0,
      maxProducts: 0,
      maxTransactions: 0,
      maxTeamMembers: 0,
      features: this.getFeatureAccess(level, plan),
    }

    // Set limits based on level and plan
    switch (level) {
      case "visitor":
        basePermissions.maxProducts = 0
        basePermissions.maxTransactions = 0
        basePermissions.maxTeamMembers = 0
        break
      case "registered":
        basePermissions.maxProducts = 3
        basePermissions.maxTransactions = 10
        basePermissions.maxTeamMembers = 0
        break
      case "onboarded":
        switch (plan) {
          case "free":
            basePermissions.maxProducts = 50
            basePermissions.maxTransactions = 100
            basePermissions.maxTeamMembers = 1
            break
          case "starter":
            basePermissions.maxProducts = 200
            basePermissions.maxTransactions = 500
            basePermissions.maxTeamMembers = 3
            break
          case "growth":
            basePermissions.maxProducts = 1000
            basePermissions.maxTransactions = 2000
            basePermissions.maxTeamMembers = 10
            break
          case "enterprise":
            basePermissions.maxProducts = -1 // unlimited
            basePermissions.maxTransactions = -1
            basePermissions.maxTeamMembers = -1
            break
        }
        break
    }

    return basePermissions
  }

  private static getFeatureAccess(level: UserLevel, plan: PlanType): FeatureAccess {
    const features: FeatureAccess = {
      // Level 1 - Discovery Phase (always available)
      publicContent: true,
      demoMode: true,
      roiCalculator: true,

      // Level 2 - Registration Phase
      basicDashboard: level !== "visitor",
      limitedProductEntry: level !== "visitor",
      sampleTransactions: level !== "visitor",
      basicOnboarding: level !== "visitor",
      languageSettings: level !== "visitor",

      // Level 3 - Onboarding Complete
      fullDashboard: level === "onboarded",
      realTimeAnalytics: level === "onboarded",
      aiInsights: level === "onboarded",
      teamManagement: level === "onboarded",
      fullReporting: level === "onboarded",
      dataExport: level === "onboarded",
      integrations: level === "onboarded",
      multiLocation: level === "onboarded" && ["growth", "enterprise"].includes(plan),
      offlineSync: level === "onboarded",

      // Plan-specific features
      apiAccess: level === "onboarded" && ["growth", "enterprise"].includes(plan),
      whatsappIntegration: level === "onboarded" && ["starter", "growth", "enterprise"].includes(plan),
      customBranding: level === "onboarded" && ["growth", "enterprise"].includes(plan),
      prioritySupport: level === "onboarded" && ["growth", "enterprise"].includes(plan),
      advancedReporting: level === "onboarded" && ["growth", "enterprise"].includes(plan),
    }

    return features
  }

  static canAccess(permissions: UserPermissions, feature: keyof FeatureAccess): boolean {
    return permissions.features[feature]
  }

  static getUpgradeMessage(feature: keyof FeatureAccess, currentLevel: UserLevel): string {
    const messages = {
      fullDashboard: "Complete your setup to unlock your full business dashboard with real-time insights!",
      realTimeAnalytics: "Finish onboarding to see live analytics and AI-powered business insights.",
      aiInsights: "Get AI-powered forecasting and recommendations by completing your business setup.",
      teamManagement: "Add team members and manage permissions after completing onboarding.",
      fullReporting: "Access comprehensive reports and data exports with full account setup.",
      dataExport: "Export your data in multiple formats after completing onboarding.",
      integrations: "Connect with payment systems and bank feeds after setup completion.",
      apiAccess: "Upgrade to Growth plan for full API access and custom integrations.",
      whatsappIntegration: "Upgrade to Starter plan or higher for WhatsApp Business integration.",
      customBranding: "Upgrade to Growth plan for custom branding and white-label options.",
      prioritySupport: "Upgrade to Growth plan for priority support and dedicated assistance.",
      advancedReporting: "Upgrade to Growth plan for advanced reporting and analytics.",
    }

    return messages[feature] || "Complete your setup to unlock this feature."
  }

  static getNextAction(feature: keyof FeatureAccess, currentLevel: UserLevel): string {
    if (currentLevel === "visitor") {
      return "Sign up for free to get started"
    }
    if (currentLevel === "registered") {
      return "Complete setup to unlock"
    }
    return "Upgrade plan to access"
  }
}
