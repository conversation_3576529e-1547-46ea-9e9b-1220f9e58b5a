import type { InventoryLevel, InventoryMovement } from "@/lib/types/product"

export class InventoryService {
  /**
   * Calculate available quantity (on hand - reserved)
   */
  static calculateAvailableQuantity(onHand: number, reserved: number): number {
    return Math.max(0, onHand - reserved)
  }

  /**
   * Check if product is low stock
   */
  static isLowStock(inventory: InventoryLevel): boolean {
    return inventory.quantityAvailable <= inventory.reorderPoint
  }

  /**
   * Calculate reorder suggestion
   */
  static calculateReorderSuggestion(inventory: InventoryLevel): {
    shouldReorder: boolean
    suggestedQuantity: number
    daysUntilStockout?: number
  } {
    const shouldReorder = this.isLowStock(inventory)
    const suggestedQuantity = shouldReorder ? inventory.reorderQuantity : 0

    return {
      shouldReorder,
      suggestedQuantity,
      // TODO: Calculate based on sales velocity
      daysUntilStockout: shouldReorder ? Math.floor(inventory.quantityAvailable / 5) : undefined,
    }
  }

  /**
   * Validate inventory movement
   */
  static validateMovement(
    currentQuantity: number,
    movementType: "IN" | "OUT" | "ADJUSTMENT" | "TRANSFER",
    quantity: number,
  ): { isValid: boolean; errors?: string[] } {
    const errors: string[] = []

    if (quantity === 0) {
      errors.push("Quantity cannot be zero")
    }

    if (movementType === "OUT" && Math.abs(quantity) > currentQuantity) {
      errors.push("Cannot remove more stock than available")
    }

    if (movementType === "ADJUSTMENT") {
      const newQuantity = currentQuantity + quantity
      if (newQuantity < 0) {
        errors.push("Adjustment would result in negative stock")
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
    }
  }

  /**
   * Calculate new inventory levels after movement
   */
  static calculateNewLevels(
    current: InventoryLevel,
    movementType: "IN" | "OUT" | "ADJUSTMENT" | "TRANSFER",
    quantity: number,
  ): Partial<InventoryLevel> {
    let newOnHand = current.quantityOnHand

    switch (movementType) {
      case "IN":
        newOnHand += Math.abs(quantity)
        break
      case "OUT":
        newOnHand -= Math.abs(quantity)
        break
      case "ADJUSTMENT":
        newOnHand += quantity // Can be positive or negative
        break
      case "TRANSFER":
        newOnHand -= Math.abs(quantity) // Outgoing transfer
        break
    }

    const newAvailable = this.calculateAvailableQuantity(newOnHand, current.quantityReserved)

    return {
      quantityOnHand: Math.max(0, newOnHand),
      quantityAvailable: Math.max(0, newAvailable),
    }
  }

  /**
   * Generate inventory movement record
   */
  static createMovementRecord(
    productId: string,
    locationId: string,
    movementType: "IN" | "OUT" | "ADJUSTMENT" | "TRANSFER",
    quantity: number,
    reason: string,
    userId: string,
    variantId?: string,
    reference?: string,
  ): Omit<InventoryMovement, "id" | "createdAt"> {
    return {
      productId,
      variantId,
      locationId,
      movementType,
      quantity,
      reason,
      reference,
      userId,
    }
  }
}
