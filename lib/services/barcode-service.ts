import type { BarcodeValidation } from "@/lib/types/product"

export class BarcodeService {
  /**
   * Validate barcode format and check digit
   */
  static validateBarcode(barcode: string, type: string): BarcodeValidation {
    const cleanBarcode = barcode.replace(/\s/g, "")

    switch (type) {
      case "EAN13":
        return this.validateEAN13(cleanBarcode)
      case "EAN8":
        return this.validateEAN8(cleanBarcode)
      case "UPC":
        return this.validateUPC(cleanBarcode)
      case "CODE128":
        return this.validateCode128(cleanBarcode)
      case "CODE39":
        return this.validateCode39(cleanBarcode)
      default:
        return { isValid: false, errors: ["Unknown barcode type"] }
    }
  }

  /**
   * Generate EAN-13 barcode with check digit
   */
  static generateEAN13(prefix = "200"): string {
    // Generate random 9 digits after prefix
    const randomDigits = Math.floor(Math.random() * 1000000000)
      .toString()
      .padStart(9, "0")
    const withoutCheckDigit = prefix + randomDigits
    const checkDigit = this.calculateEAN13CheckDigit(withoutCheckDigit)
    return withoutCheckDigit + checkDigit
  }

  /**
   * Generate SKU based on category and sequence
   */
  static generateSKU(categoryCode: string, sequence: number): string {
    return `${categoryCode.toUpperCase()}-${sequence.toString().padStart(6, "0")}`
  }

  private static validateEAN13(barcode: string): BarcodeValidation {
    if (!/^\d{13}$/.test(barcode)) {
      return { isValid: false, errors: ["EAN-13 must be exactly 13 digits"] }
    }

    const checkDigit = Number.parseInt(barcode[12])
    const calculatedCheckDigit = this.calculateEAN13CheckDigit(barcode.slice(0, 12))

    if (checkDigit !== calculatedCheckDigit) {
      return {
        isValid: false,
        errors: ["Invalid check digit"],
        checkDigit: calculatedCheckDigit,
      }
    }

    return { isValid: true, type: "EAN13", checkDigit }
  }

  private static validateEAN8(barcode: string): BarcodeValidation {
    if (!/^\d{8}$/.test(barcode)) {
      return { isValid: false, errors: ["EAN-8 must be exactly 8 digits"] }
    }

    const checkDigit = Number.parseInt(barcode[7])
    const calculatedCheckDigit = this.calculateEAN8CheckDigit(barcode.slice(0, 7))

    if (checkDigit !== calculatedCheckDigit) {
      return {
        isValid: false,
        errors: ["Invalid check digit"],
        checkDigit: calculatedCheckDigit,
      }
    }

    return { isValid: true, type: "EAN8", checkDigit }
  }

  private static validateUPC(barcode: string): BarcodeValidation {
    if (!/^\d{12}$/.test(barcode)) {
      return { isValid: false, errors: ["UPC must be exactly 12 digits"] }
    }

    const checkDigit = Number.parseInt(barcode[11])
    const calculatedCheckDigit = this.calculateUPCCheckDigit(barcode.slice(0, 11))

    if (checkDigit !== calculatedCheckDigit) {
      return {
        isValid: false,
        errors: ["Invalid check digit"],
        checkDigit: calculatedCheckDigit,
      }
    }

    return { isValid: true, type: "UPC", checkDigit }
  }

  private static validateCode128(barcode: string): BarcodeValidation {
    if (!/^[\x00-\x7F]+$/.test(barcode)) {
      return { isValid: false, errors: ["Code 128 contains invalid characters"] }
    }

    if (barcode.length < 1 || barcode.length > 80) {
      return { isValid: false, errors: ["Code 128 must be 1-80 characters"] }
    }

    return { isValid: true, type: "CODE128" }
  }

  private static validateCode39(barcode: string): BarcodeValidation {
    if (!/^[A-Z0-9\-. $/+%*]+$/.test(barcode)) {
      return { isValid: false, errors: ["Code 39 contains invalid characters"] }
    }

    return { isValid: true, type: "CODE39" }
  }

  private static calculateEAN13CheckDigit(barcode: string): number {
    let sum = 0
    for (let i = 0; i < 12; i++) {
      const digit = Number.parseInt(barcode[i])
      sum += i % 2 === 0 ? digit : digit * 3
    }
    return (10 - (sum % 10)) % 10
  }

  private static calculateEAN8CheckDigit(barcode: string): number {
    let sum = 0
    for (let i = 0; i < 7; i++) {
      const digit = Number.parseInt(barcode[i])
      sum += i % 2 === 0 ? digit * 3 : digit
    }
    return (10 - (sum % 10)) % 10
  }

  private static calculateUPCCheckDigit(barcode: string): number {
    let sum = 0
    for (let i = 0; i < 11; i++) {
      const digit = Number.parseInt(barcode[i])
      sum += i % 2 === 0 ? digit * 3 : digit
    }
    return (10 - (sum % 10)) % 10
  }
}
