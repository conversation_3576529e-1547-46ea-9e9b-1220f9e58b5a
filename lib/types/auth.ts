export interface User {
  id: string
  email: string
  name: string
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  loginAttempts: number
  lockedUntil?: Date
  passwordResetToken?: string
  passwordResetExpires?: Date
  emailVerificationToken?: string
}

export interface AuthSession {
  userId: string
  email: string
  name: string
  sessionId: string
  expiresAt: Date
  userAgent?: string
  ipAddress?: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface ResetPasswordRequest {
  email: string
}

export interface ChangePasswordRequest {
  token: string
  password: string
  confirmPassword: string
}

export interface AuthResponse {
  success: boolean
  message: string
  user?: Omit<User, "passwordHash">
  token?: string
  refreshToken?: string
}

export interface SessionValidationResponse {
  valid: boolean
  user?: Omit<User, "passwordHash">
  session?: AuthSession
}
