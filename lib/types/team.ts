export type Role = "owner" | "admin" | "member" | "viewer"

export interface TeamMember {
  id: string
  name: string
  email: string
  role: Role
  avatar?: string
  joinedAt: string
  lastActive?: string
}

export interface Team {
  id: string
  name: string
  members: TeamMember[]
  createdAt: string
  updatedAt: string
}

export interface Permission {
  id: string
  name: string
  description: string
  roles: Role[]
}

export const defaultPermissions: Permission[] = [
  {
    id: "view_dashboard",
    name: "View Dashboard",
    description: "Can view the main dashboard",
    roles: ["owner", "admin", "member", "viewer"],
  },
  {
    id: "manage_team",
    name: "Manage Team",
    description: "Can add, remove, and update team members",
    roles: ["owner", "admin"],
  },
  {
    id: "manage_billing",
    name: "Manage Billing",
    description: "Can view and update billing information",
    roles: ["owner", "admin"],
  },
  {
    id: "view_reports",
    name: "View Reports",
    description: "Can view reports and analytics",
    roles: ["owner", "admin", "member"],
  },
  {
    id: "export_data",
    name: "Export Data",
    description: "Can export data from the system",
    roles: ["owner", "admin", "member"],
  },
  {
    id: "manage_settings",
    name: "Manage Settings",
    description: "Can update system settings",
    roles: ["owner", "admin"],
  },
]

export function hasPermission(userRole: Role, permissionId: string): boolean {
  const permission = defaultPermissions.find((p) => p.id === permissionId)
  if (!permission) return false
  return permission.roles.includes(userRole)
}
