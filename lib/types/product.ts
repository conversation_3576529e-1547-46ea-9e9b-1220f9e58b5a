export interface Product {
  id: string
  organizationId: string
  categoryId?: string
  name: string
  description?: string
  sku: string
  barcode: string
  barcodeType: "EAN13" | "EAN8" | "UPC" | "CODE128" | "CODE39"
  costPrice: number
  sellingPrice: number
  vatRate: number
  trackInventory: boolean
  lowStockThreshold: number
  weight?: number
  dimensions?: {
    length?: number
    width?: number
    height?: number
  }
  image?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  category?: Category
  variants?: ProductVariant[]
  inventory?: InventoryLevel[]
}

export interface ProductVariant {
  id: string
  productId: string
  name: string
  sku: string
  barcode: string
  costPrice?: number
  sellingPrice?: number
  attributes: Record<string, string>
  isActive: boolean
}

export interface Category {
  id: string
  organizationId: string
  name: string
  description?: string
  parentId?: string
  vatRate: number
  createdAt: string
  updatedAt: string
  children?: Category[]
}

export interface InventoryLevel {
  id: string
  productId: string
  variantId?: string
  locationId: string
  quantityOnHand: number
  quantityReserved: number
  quantityAvailable: number
  reorderPoint: number
  reorderQuantity: number
  lastSyncAt?: string
  location?: {
    id: string
    name: string
    code: string
  }
}

export interface InventoryMovement {
  id: string
  productId: string
  variantId?: string
  locationId: string
  movementType: "IN" | "OUT" | "ADJUSTMENT" | "TRANSFER"
  quantity: number
  reason: string
  reference?: string
  userId: string
  createdAt: string
}

export interface BarcodeValidation {
  isValid: boolean
  type?: string
  checkDigit?: number
  errors?: string[]
}
