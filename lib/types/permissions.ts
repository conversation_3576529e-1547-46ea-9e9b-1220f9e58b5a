export type UserLevel = "visitor" | "registered" | "onboarded"
export type PlanType = "free" | "starter" | "growth" | "enterprise"

export interface UserPermissions {
  level: UserLevel
  plan: PlanType
  isEmailVerified: boolean
  onboardingStep: number
  maxProducts: number
  maxTransactions: number
  maxTeamMembers: number
  features: FeatureAccess
}

export interface FeatureAccess {
  // Level 1 - Discovery Phase
  publicContent: boolean
  demoMode: boolean
  roiCalculator: boolean

  // Level 2 - Registration Phase
  basicDashboard: boolean
  limitedProductEntry: boolean
  sampleTransactions: boolean
  basicOnboarding: boolean
  languageSettings: boolean

  // Level 3 - Onboarding Complete
  fullDashboard: boolean
  realTimeAnalytics: boolean
  aiInsights: boolean
  teamManagement: boolean
  fullReporting: boolean
  dataExport: boolean
  integrations: boolean
  multiLocation: boolean
  offlineSync: boolean

  // Plan-specific features
  apiAccess: boolean
  whatsappIntegration: boolean
  customBranding: boolean
  prioritySupport: boolean
  advancedReporting: boolean
}

export interface AccessGate {
  feature: keyof FeatureAccess
  requiredLevel: UserLevel
  requiredPlan?: PlanType
  upgradeMessage: string
  upgradeAction: string
}
