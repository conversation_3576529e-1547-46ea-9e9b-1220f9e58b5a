// South African VAT number validation regex
// Format: 4 digits followed by 6 digits, e.g. 4220159380
export const saVatValidator = /^4\d{9}$/

// South African ID number validation regex
export const saIdValidator =
  /^(((\d{2}((0[13578]|1[02])(0[1-9]|[12]\d|3[01])|(0[13456789]|1[012])(0[1-9]|[12]\d|30)|02(0[1-9]|1\d|2[0-8])))|([02468][048]|[13579][26])0229))(( |-)(\d{4})( |-)(\d{3})|(\d{7}))/

// South African phone number validation regex
export const saPhoneValidator = /^(\+27|0)[6-8][0-9]{8}$/

// South African postal code validation regex
export const saPostalValidator = /^\d{4}$/

// TODO: IMPLEMENT SA TAX CALCULATOR - Add tax calculation functions
