import { z } from "zod"

export const productSchema = z.object({
  name: z.string().min(1, "Product name is required").max(255),
  description: z.string().optional(),
  sku: z.string().min(1, "SKU is required").max(100),
  barcode: z.string().min(1, "Barcode is required").max(50),
  barcodeType: z.enum(["EAN13", "EAN8", "UPC", "CODE128", "CODE39"]),
  categoryId: z.string().uuid().optional(),
  costPrice: z.number().min(0, "Cost price must be positive"),
  sellingPrice: z.number().min(0, "Selling price must be positive"),
  vatRate: z.number().min(0).max(100, "VAT rate must be between 0 and 100"),
  trackInventory: z.boolean().default(true),
  lowStockThreshold: z.number().int().min(0).default(10),
  weight: z.number().positive().optional(),
  dimensions: z
    .object({
      length: z.number().positive().optional(),
      width: z.number().positive().optional(),
      height: z.number().positive().optional(),
    })
    .optional(),
  isActive: z.boolean().default(true),
})

export const categorySchema = z.object({
  name: z.string().min(1, "Category name is required").max(255),
  description: z.string().optional(),
  parentId: z.string().uuid().optional(),
  vatRate: z.number().min(0).max(100).default(15),
})

export const inventoryAdjustmentSchema = z.object({
  productId: z.string().uuid(),
  variantId: z.string().uuid().optional(),
  locationId: z.string().uuid(),
  quantity: z.number().int(),
  reason: z.string().min(1, "Reason is required"),
  reference: z.string().optional(),
})

export const productVariantSchema = z.object({
  name: z.string().min(1, "Variant name is required"),
  sku: z.string().min(1, "SKU is required"),
  barcode: z.string().min(1, "Barcode is required"),
  costPrice: z.number().min(0).optional(),
  sellingPrice: z.number().min(0).optional(),
  attributes: z.record(z.string()),
  isActive: z.boolean().default(true),
})

export type ProductFormData = z.infer<typeof productSchema>
export type CategoryFormData = z.infer<typeof categorySchema>
export type InventoryAdjustmentData = z.infer<typeof inventoryAdjustmentSchema>
export type ProductVariantData = z.infer<typeof productVariantSchema>
