import { create } from "zustand"
import type { UserPermissions, UserLevel, PlanType } from "@/lib/types/permissions"
import { PermissionsService } from "@/lib/services/permissions-service"

interface PermissionsState {
  permissions: UserPermissions
  setUserLevel: (level: UserLevel) => void
  setPlan: (plan: PlanType) => void
  setEmailVerified: (verified: boolean) => void
  updateOnboardingStep: (step: number) => void
  canAccess: (feature: keyof UserPermissions["features"]) => boolean
  getUpgradeInfo: (feature: keyof UserPermissions["features"]) => { message: string; action: string }
}

export const usePermissionsStore = create<PermissionsState>((set, get) => ({
  permissions: PermissionsService.getDefaultPermissions("visitor", "free"),

  setUserLevel: (level) =>
    set((state) => ({
      permissions: PermissionsService.getDefaultPermissions(level, state.permissions.plan),
    })),

  setPlan: (plan) =>
    set((state) => ({
      permissions: PermissionsService.getDefaultPermissions(state.permissions.level, plan),
    })),

  setEmailVerified: (verified) =>
    set((state) => ({
      permissions: { ...state.permissions, isEmailVerified: verified },
    })),

  updateOnboardingStep: (step) =>
    set((state) => ({
      permissions: { ...state.permissions, onboardingStep: step },
    })),

  canAccess: (feature) => {
    const { permissions } = get()
    return PermissionsService.canAccess(permissions, feature)
  },

  getUpgradeInfo: (feature) => {
    const { permissions } = get()
    return {
      message: PermissionsService.getUpgradeMessage(feature, permissions.level),
      action: PermissionsService.getNextAction(feature, permissions.level),
    }
  },
}))
