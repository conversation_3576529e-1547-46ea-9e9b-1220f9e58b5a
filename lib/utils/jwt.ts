import jwt from "jsonwebtoken"
import { SECURITY_CONFIG } from "@/lib/config/security"
import type { User } from "@/lib/types/auth"

export interface JWTPayload {
  userId: string
  email: string
  sessionId: string
  type: "access" | "refresh"
}

export class JWTUtils {
  static generateAccessToken(user: User, sessionId: string): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      sessionId,
      type: "access",
    }

    return jwt.sign(payload, SECURITY_CONFIG.JWT_SECRET, {
      expiresIn: "24h",
      issuer: "flowiq-auth",
      audience: "flowiq-platform",
    })
  }

  static generateRefreshToken(user: User, sessionId: string): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      sessionId,
      type: "refresh",
    }

    return jwt.sign(payload, SECURITY_CONFIG.JWT_REFRESH_SECRET, {
      expiresIn: "7d",
      issuer: "flowiq-auth",
      audience: "flowiq-platform",
    })
  }

  static verifyAccessToken(token: string): JWTPayload | null {
    try {
      const payload = jwt.verify(token, SECURITY_CONFIG.JWT_SECRET, {
        issuer: "flowiq-auth",
        audience: "flowiq-platform",
      }) as JWTPayload

      if (payload.type !== "access") {
        return null
      }

      return payload
    } catch (error) {
      return null
    }
  }

  static verifyRefreshToken(token: string): JWTPayload | null {
    try {
      const payload = jwt.verify(token, SECURITY_CONFIG.JWT_REFRESH_SECRET, {
        issuer: "flowiq-auth",
        audience: "flowiq-platform",
      }) as JWTPayload

      if (payload.type !== "refresh") {
        return null
      }

      return payload
    } catch (error) {
      return null
    }
  }
}
