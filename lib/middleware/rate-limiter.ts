import { RateLimiterMemory } from "rate-limiter-flexible"
import { type NextRequest, NextResponse } from "next/server"
import { SECURITY_CONFIG } from "@/lib/config/security"

const loginLimiter = new RateLimiterMemory({
  keyGenerator: (req: NextRequest) => req.ip || "unknown",
  points: SECURITY_CONFIG.LOGIN_RATE_LIMIT.max,
  duration: SECURITY_CONFIG.LOGIN_RATE_LIMIT.windowMs / 1000,
})

const registerLimiter = new RateLimiterMemory({
  keyGenerator: (req: NextRequest) => req.ip || "unknown",
  points: SECURITY_CONFIG.REGISTER_RATE_LIMIT.max,
  duration: SECURITY_CONFIG.REGISTER_RATE_LIMIT.windowMs / 1000,
})

const passwordResetLimiter = new RateLimiterMemory({
  keyGenerator: (req: NextRequest) => req.ip || "unknown",
  points: SECURITY_CONFIG.PASSWORD_RESET_RATE_LIMIT.max,
  duration: SECURITY_CONFIG.PASSWORD_RESET_RATE_LIMIT.windowMs / 1000,
})

export async function applyRateLimit(
  request: NextRequest,
  type: "login" | "register" | "password-reset",
): Promise<NextResponse | null> {
  try {
    let limiter: RateLimiterMemory
    if (type === "login") {
      limiter = loginLimiter
    } else if (type === "register") {
      limiter = registerLimiter
    } else {
      limiter = passwordResetLimiter
    }

    await limiter.consume(request.ip || "unknown")
    return null // No rate limit hit
  } catch (rateLimiterRes) {
    const remainingTime = Math.round((rateLimiterRes as any).msBeforeNext / 1000)
    const limiter = type === "login" ? loginLimiter : type === "register" ? registerLimiter : passwordResetLimiter

    return NextResponse.json(
      {
        success: false,
        message: `Too many attempts. Try again in ${remainingTime} seconds.`,
        retryAfter: remainingTime,
      },
      {
        status: 429,
        headers: {
          "Retry-After": remainingTime.toString(),
          "X-RateLimit-Limit": limiter.points.toString(),
          "X-RateLimit-Remaining": "0",
          "X-RateLimit-Reset": new Date(Date.now() + (rateLimiterRes as any).msBeforeNext).toISOString(),
        },
      },
    )
  }
}
