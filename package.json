{"name": "flowiq-workspace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "dev:dashboard": "npm run dev --workspace=applications/dashboard", "dev:marketing": "npm run dev --workspace=applications/marketing", "dev:auth-service": "npm run dev --workspace=applications/auth-service", "dev:all": "concurrently \"npm run dev:dashboard\" \"npm run dev:marketing\" \"npm run dev:auth-service\"", "build:dashboard": "npm run build --workspace=applications/dashboard", "build:marketing": "npm run build --workspace=applications/marketing", "build:auth-service": "npm run build --workspace=applications/auth-service", "build:all": "npm run build:dashboard && npm run build:marketing && npm run build:auth-service", "start:dashboard": "npm run start --workspace=applications/dashboard", "start:marketing": "npm run start --workspace=applications/marketing", "start:auth-service": "npm run start --workspace=applications/auth-service", "lint:dashboard": "npm run lint --workspace=applications/dashboard", "lint:marketing": "npm run lint --workspace=applications/marketing", "lint:auth-service": "npm run lint --workspace=applications/auth-service", "lint:all": "npm run lint:dashboard && npm run lint:marketing && npm run lint:auth-service", "type-check": "npm run type-check --workspace=applications/dashboard && npm run type-check --workspace=applications/marketing && npm run type-check --workspace=applications/auth-service", "clean": "rm -rf node_modules applications/*/node_modules shared-packages/*/node_modules apps/*/node_modules packages/*/node_modules", "clean:build": "rm -rf applications/*/.next apps/*/.next .next", "setup": "chmod +x scripts/quick-start.sh && ./scripts/quick-start.sh", "setup:backend": "cd backend && chmod +x scripts/setup.sh && ./scripts/setup.sh", "test": "npm run test --workspace=applications/auth-service", "test:all": "npm run test --workspace=applications/auth-service"}, "dependencies": {"@auth/core": "latest", "@flowiq/shared-types": "latest", "@flowiq/shared-utils": "latest", "@flowiq/ui": "latest", "@flowiq/ui-components": "latest", "@hookform/resolvers": "latest", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "immer": "latest", "input-otp": "latest", "jsonwebtoken": "latest", "lucide-react": "^0.454.0", "next": "14.2.16", "next-auth": "latest", "next-themes": "latest", "nodemailer": "latest", "rate-limiter-flexible": "latest", "react": "^18", "react-day-picker": "latest", "react-dom": "^18", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "latest", "vaul": "latest", "zod": "latest", "zustand": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "concurrently": "^8.2.2", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}