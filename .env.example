# FlowIQ Frontend Environment Variables
# Copy this file to .env.local and update with your actual values

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================

# JWT Secrets (CHANGE IN PRODUCTION!)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-long-and-random
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-different-from-jwt

# NextAuth Configuration
NEXTAUTH_SECRET=your-nextauth-secret-key-change-in-production
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Auth Service URL
NEXT_PUBLIC_AUTH_SERVICE_URL=http://localhost:3002

# =============================================================================
# CORS & SECURITY
# =============================================================================

# Allowed origins for CORS (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# =============================================================================
# EXTERNAL SERVICES (OPTIONAL)
# =============================================================================

# Mapbox for location services
NEXT_PUBLIC_MAPBOX_TOKEN=your-mapbox-access-token

# Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your-google-analytics-id

# Sentry for error tracking
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Environment
NODE_ENV=development

# Debug mode
NEXT_PUBLIC_DEBUG=true

# =============================================================================
# SOUTH AFRICAN SPECIFIC
# =============================================================================

# Default currency
NEXT_PUBLIC_DEFAULT_CURRENCY=ZAR

# Default timezone
NEXT_PUBLIC_DEFAULT_TIMEZONE=Africa/Johannesburg

# VAT Rate
NEXT_PUBLIC_VAT_RATE=0.15

# =============================================================================
# PAYMENT INTEGRATION (FUTURE)
# =============================================================================

# PayFast (South African payment gateway)
# NEXT_PUBLIC_PAYFAST_MERCHANT_ID=your-payfast-merchant-id
# NEXT_PUBLIC_PAYFAST_MERCHANT_KEY=your-payfast-merchant-key
# PAYFAST_PASSPHRASE=your-payfast-passphrase

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Email service for notifications
EMAIL_FROM=<EMAIL>

# =============================================================================
# FILE STORAGE (FUTURE)
# =============================================================================

# AWS S3 or DigitalOcean Spaces
# NEXT_PUBLIC_STORAGE_BUCKET=flowiq-storage
# NEXT_PUBLIC_STORAGE_REGION=fra1

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable features
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_BARCODE_SCANNING=true

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env.local
# 2. Update all "your-*" placeholders with actual values
# 3. Generate secure random strings for JWT secrets
# 4. Never commit .env.local to version control
# 5. Use different values for development, staging, and production
